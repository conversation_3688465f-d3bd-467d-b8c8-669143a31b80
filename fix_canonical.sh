#!/bin/bash

# Fix canonical tags for all HTML files
DOMAIN="https://stringworks-cymta.b-cdn.net"

# Default language pages (Greek)
sed -i 's|<link rel="canonical" href="/find" />|<link rel="canonical" href="'$DOMAIN'/find/" />|g' find/index.html
sed -i 's|<link rel="canonical" href="/mt" />|<link rel="canonical" href="'$DOMAIN'/mt/" />|g' mt/index.html
sed -i 's|<link rel="canonical" href="/mt-info" />|<link rel="canonical" href="'$DOMAIN'/mt-info/" />|g' mt-info/index.html
sed -i 's|<link rel="canonical" href="/mt-blog" />|<link rel="canonical" href="'$DOMAIN'/mt-blog/" />|g' mt-blog/index.html
sed -i 's|<link rel="canonical" href="/study" />|<link rel="canonical" href="'$DOMAIN'/study/" />|g' study/index.html
sed -i 's|<link rel="canonical" href="/blog" />|<link rel="canonical" href="'$DOMAIN'/blog/" />|g' blog/index.html
sed -i 's|<link rel="canonical" href="/faq" />|<link rel="canonical" href="'$DOMAIN'/faq/" />|g' faq/index.html
sed -i 's|<link rel="canonical" href="/mt-autism" />|<link rel="canonical" href="'$DOMAIN'/mt-autism/" />|g' mt-autism/index.html
sed -i 's|<link rel="canonical" href="/mt-special-moments" />|<link rel="canonical" href="'$DOMAIN'/mt-special-moments/" />|g' mt-special-moments/index.html

# English pages
sed -i 's|<link rel="canonical" href="/en/home" />|<link rel="canonical" href="'$DOMAIN'/en/home/" />|g' en/home/<USER>
sed -i 's|<link rel="canonical" href="/en/contact" />|<link rel="canonical" href="'$DOMAIN'/en/contact/" />|g' en/contact/index.html
sed -i 's|<link rel="canonical" href="/en/find" />|<link rel="canonical" href="'$DOMAIN'/en/find/" />|g' en/find/index.html
sed -i 's|<link rel="canonical" href="/en/mt" />|<link rel="canonical" href="'$DOMAIN'/en/mt/" />|g' en/mt/index.html
sed -i 's|<link rel="canonical" href="/en/mt-info" />|<link rel="canonical" href="'$DOMAIN'/en/mt-info/" />|g' en/mt-info/index.html
sed -i 's|<link rel="canonical" href="/en/mt-blog" />|<link rel="canonical" href="'$DOMAIN'/en/mt-blog/" />|g' en/mt-blog/index.html
sed -i 's|<link rel="canonical" href="/en/study" />|<link rel="canonical" href="'$DOMAIN'/en/study/" />|g' en/study/index.html

echo "Canonical tags updated successfully!"
