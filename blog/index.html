<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8" />
	<meta property="og:site_name" content="" />
	<meta property="og:type" content="article" />
	<meta property="og:url" content="/blog" />
	<meta content="summary_large_image" name="twitter:card" />
	<meta property="og:title" content="Blog" />
	<meta name="twitter:title" content="Blog" />
	<meta property="og:description" content="" />
	<meta name="twitter:description" content="" />
	<title>Blog</title>
	<meta name="description" content="" />
	<link rel="canonical" href="https://stringworks-cymta.b-cdn.net/blog/" />
	<link rel="icon" href="https://cloud-1de12d.b-cdn.net/media/iW=32%26iH=any/5800e034b05aa57cedaa6e50ab5154b2.png"
		sizes="32x32" />
	<link rel="icon" href="https://cloud-1de12d.b-cdn.net/media/iW=192%26iH=any/5800e034b05aa57cedaa6e50ab5154b2.png"
		sizes="192x192" />
	<link rel="apple-touch-icon-precomposed"
		href="https://cloud-1de12d.b-cdn.net/media/iW=180&iH=any/5800e034b05aa57cedaa6e50ab5154b2.png" />
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link class="brz-link brz-link-bunny-fonts-prefetch" rel="dns-prefetch" href="//fonts.bunny.net">
	<link class="brz-link brz-link-bunny-fonts-preconnect" rel="preconnect" href="https://fonts.bunny.net/" crossorigin>
	<link class="brz-link brz-link-cdn-preconnect" rel="preconnect" href="https://cloud-1de12d.b-cdn.net" crossorigin>
	<link
		href="https://fonts.bunny.net/css?family=Lato:100,100italic,300,300italic,regular,italic,700,700italic,900,900italic|Comfortaa:300,regular,500,600,700&subset=arabic,bengali,cyrillic,cyrillic-ext,devanagari,greek,greek-ext,gujarati,hebrew,khmer,korean,latin-ext,tamil,telugu,thai,vietnamese&display=swap"
		class="brz-link brz-link-google" type="text/css" rel="stylesheet" />
	<link href="../assets/07575d7e3674e1a8b8d8a30025ca06cb.css" class="brz-link brz-link-preview-lib-pro"
		data-brz-group="group-1_2" rel="stylesheet" />
	<link href="../assets/a1c351b066e704f38f6ab6530f000598.css" class="brz-link brz-link-preview-pro"
		rel="stylesheet" />
	<style class="brz-style">
		@media (min-width:991px) {
			.brz .brz-css-sk5zf3 {
				display: block;
			}
		}

		@media (max-width:991px) and (min-width:768px) {
			.brz .brz-css-sk5zf3 {
				display: block;
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-sk5zf3 {
				display: block;
			}
		}

		@media (min-width:991px) {
			.brz .brz-css-c8fq4o:hover {
				display: block;
			}
		}

		.brz .brz-css-1wzn4p1 {
			padding: 75px 0px 75px 0px;
			margin: 0;
		}

		.brz .brz-css-1wzn4p1>.brz-bg {
			border-radius: 0px;
		}

		.brz .brz-css-1wzn4p1>.brz-bg {
			border: 0px solid rgba(102, 115, 141, 0);
		}

		.brz .brz-css-1wzn4p1>.brz-bg:after {
			box-shadow: none;
		}

		.brz .brz-css-1wzn4p1>.brz-bg>.brz-bg-image {
			-webkit-mask-image: none;
			mask-image: none;
			background-size: cover;
			background-repeat: no-repeat;
		}

		.brz .brz-css-1wzn4p1>.brz-bg>.brz-bg-image {
			background-image: none;
			filter: none;
		}

		.brz .brz-css-1wzn4p1>.brz-bg>.brz-bg-image:after {
			content: "";
			background-image: none;
		}

		.brz .brz-css-1wzn4p1>.brz-bg>.brz-bg-color {
			-webkit-mask-image: none;
			mask-image: none;
		}

		.brz .brz-css-1wzn4p1>.brz-bg>.brz-bg-color {
			background-color: rgba(0, 0, 0, 0);
			background-image: none;
		}

		.brz .brz-css-1wzn4p1>.brz-bg>.brz-bg-shape__top {
			background-size: 100% 100px;
			height: 100px;
			transform: scale(1.02) rotateX(0deg) rotateY(0deg);
			z-index: auto;
		}

		.brz .brz-css-1wzn4p1>.brz-bg>.brz-bg-shape__top::after {
			background-image: none;
			-webkit-mask-image: none;
			background-size: 100% 100px;
			height: 100px;
		}

		.brz .brz-css-1wzn4p1>.brz-bg>.brz-bg-shape__bottom {
			background-size: 100% 100px;
			height: 100px;
			transform: scale(1.02) rotateX(-180deg) rotateY(-180deg);
			z-index: auto;
		}

		.brz .brz-css-1wzn4p1>.brz-bg>.brz-bg-shape__bottom::after {
			background-image: none;
			-webkit-mask-image: none;
			background-size: 100% 100px;
			height: 100px;
		}

		@media (max-width:991px) and (min-width:768px) {
			.brz .brz-css-1wzn4p1 {
				padding: 50px 15px 50px 15px;
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-1wzn4p1 {
				padding: 25px 15px 25px 15px;
			}
		}

		.brz .brz-css-1uopts9 {
			padding: 0;
			margin: 0px 48px 0px 48px;
		}

		.brz .brz-css-1uopts9>.brz-bg>.brz-bg-color {
			background-color: rgba(17, 90, 47, 0);
		}

		@media (min-width:991px) {
			.brz .brz-css-1uopts9 {
				padding: 0;
				margin: 0px 48px 0px 48px;
			}

			.brz .brz-css-1uopts9>.brz-bg {
				border-radius: 0px;
			}

			.brz .brz-css-1uopts9:hover>.brz-bg {
				border: 0px solid rgba(102, 115, 141, 0);
			}

			.brz .brz-css-1uopts9:hover>.brz-bg:after {
				box-shadow: none;
			}

			.brz .brz-css-1uopts9>.brz-bg>.brz-bg-image {
				-webkit-mask-image: none;
				mask-image: none;
				background-size: cover;
				background-repeat: no-repeat;
			}

			.brz .brz-css-1uopts9:hover>.brz-bg>.brz-bg-image {
				background-image: none;
				filter: none;
			}

			.brz .brz-css-1uopts9:hover>.brz-bg>.brz-bg-image:after {
				content: "";
				background-image: none;
			}

			.brz .brz-css-1uopts9>.brz-bg>.brz-bg-color {
				-webkit-mask-image: none;
				mask-image: none;
			}

			.brz .brz-css-1uopts9:hover>.brz-bg>.brz-bg-color {
				background-color: rgba(17, 90, 47, 0);
				background-image: none;
			}

			.brz .brz-css-1uopts9>.brz-bg>.brz-bg-shape__top {
				background-size: 100% 100px;
				height: 100px;
				transform: scale(1.02) rotateX(0deg) rotateY(0deg);
				z-index: auto;
			}

			.brz .brz-css-1uopts9>.brz-bg>.brz-bg-shape__top::after {
				background-image: none;
				-webkit-mask-image: none;
				background-size: 100% 100px;
				height: 100px;
			}

			.brz .brz-css-1uopts9>.brz-bg>.brz-bg-shape__bottom {
				background-size: 100% 100px;
				height: 100px;
				transform: scale(1.02) rotateX(-180deg) rotateY(-180deg);
				z-index: auto;
			}

			.brz .brz-css-1uopts9>.brz-bg>.brz-bg-shape__bottom::after {
				background-image: none;
				-webkit-mask-image: none;
				background-size: 100% 100px;
				height: 100px;
			}
		}

		@media (max-width:991px) and (min-width:768px) {
			.brz .brz-css-1uopts9 {
				margin: 0;
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-1uopts9 {
				margin: 0;
			}
		}

		.brz .brz-css-2gpbzd {
			border: 0px solid transparent;
		}

		@media (min-width:991px) {
			.brz .brz-css-2gpbzd {
				max-width: calc(1 * var(--brz-section-container-max-width, 1170px));
			}
		}

		@media (max-width:991px) and (min-width:768px) {
			.brz .brz-css-2gpbzd {
				max-width: 100%;
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-2gpbzd {
				max-width: 100%;
			}
		}

		@media (min-width:991px) {
			.brz .brz-css-1efqzy4:hover {
				border: 0px solid transparent;
			}
		}

		@media (min-width:991px) {
			.brz .brz-css-1efqzy4 {
				max-width: calc(1 * var(--brz-section-container-max-width, 1170px));
			}
		}

		.brz .brz-css-1t66ezt {
			margin: 0;
			z-index: auto;
			align-items: flex-start;
		}

		.brz .brz-css-1t66ezt>.brz-bg {
			border-radius: 0px;
			max-width: 100%;
			mix-blend-mode: normal;
		}

		.brz .brz-css-1t66ezt>.brz-bg {
			border: 0px solid rgba(102, 115, 141, 0);
			box-shadow: none;
		}

		.brz .brz-css-1t66ezt>.brz-bg>.brz-bg-image {
			background-size: cover;
			background-repeat: no-repeat;
			-webkit-mask-image: none;
			mask-image: none;
		}

		.brz .brz-css-1t66ezt>.brz-bg>.brz-bg-image {
			background-image: none;
			filter: none;
			display: block;
		}

		.brz .brz-css-1t66ezt>.brz-bg>.brz-bg-image:after {
			content: "";
			background-image: none;
		}

		.brz .brz-css-1t66ezt>.brz-bg>.brz-bg-color {
			-webkit-mask-image: none;
			mask-image: none;
		}

		.brz .brz-css-1t66ezt>.brz-bg>.brz-bg-color {
			background-color: rgba(0, 0, 0, 0);
			background-image: none;
		}

		.brz .brz-css-1t66ezt>.brz-bg>.brz-bg-map {
			display: none;
		}

		.brz .brz-css-1t66ezt>.brz-bg>.brz-bg-map {
			filter: none;
		}

		.brz .brz-css-1t66ezt>.brz-bg>.brz-bg-video {
			display: none;
		}

		.brz .brz-css-1t66ezt>.brz-bg>.brz-bg-video {
			filter: none;
		}

		.brz .brz-css-1t66ezt>.brz-row {
			border: 0px solid transparent;
		}

		@media (min-width:991px) {
			.brz .brz-css-1t66ezt {
				min-height: auto;
				display: flex;
			}

			.brz .brz-css-1t66ezt>.brz-bg {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-1t66ezt>.brz-bg>.brz-bg-image {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-1t66ezt>.brz-bg>.brz-bg-color {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-1t66ezt>.brz-bg>.brz-bg-video {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-1t66ezt>.brz-row {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}
		}

		@media (max-width:991px) and (min-width:768px) {
			.brz .brz-css-1t66ezt {
				min-height: auto;
				display: flex;
			}

			.brz .brz-css-1t66ezt>.brz-bg {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-1t66ezt>.brz-bg>.brz-bg-image {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-1t66ezt>.brz-bg>.brz-bg-color {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-1t66ezt>.brz-bg>.brz-bg-video {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-1t66ezt>.brz-row {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
				flex-direction: row;
				flex-wrap: wrap;
				justify-content: flex-start;
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-1t66ezt {
				min-height: auto;
				display: flex;
			}

			.brz .brz-css-1t66ezt>.brz-bg {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-1t66ezt>.brz-bg>.brz-bg-image {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-1t66ezt>.brz-bg>.brz-bg-color {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-1t66ezt>.brz-bg>.brz-bg-video {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-1t66ezt>.brz-row {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
				flex-direction: row;
				flex-wrap: wrap;
				justify-content: flex-start;
			}
		}

		.brz .brz-css-1a7ogzm>.brz-bg {
			border-radius: 16px;
		}

		.brz .brz-css-1a7ogzm>.brz-bg>.brz-bg-color {
			background-color: rgba(var(--brz-global-color8), 1);
		}

		@media (min-width:991px) {
			.brz .brz-css-1a7ogzm {
				margin: 0;
				z-index: auto;
				align-items: flex-start;
			}

			.brz .brz-css-1a7ogzm>.brz-bg {
				border-radius: 16px;
				max-width: 100%;
				mix-blend-mode: normal;
			}

			.brz .brz-css-1a7ogzm:hover>.brz-bg {
				border: 0px solid rgba(102, 115, 141, 0);
				box-shadow: none;
			}

			.brz .brz-css-1a7ogzm>.brz-bg>.brz-bg-image {
				background-size: cover;
				background-repeat: no-repeat;
				-webkit-mask-image: none;
				mask-image: none;
			}

			.brz .brz-css-1a7ogzm:hover>.brz-bg>.brz-bg-image {
				background-image: none;
				filter: none;
				display: block;
			}

			.brz .brz-css-1a7ogzm:hover>.brz-bg>.brz-bg-image:after {
				content: "";
				background-image: none;
			}

			.brz .brz-css-1a7ogzm>.brz-bg>.brz-bg-color {
				-webkit-mask-image: none;
				mask-image: none;
			}

			.brz .brz-css-1a7ogzm:hover>.brz-bg>.brz-bg-color {
				background-color: rgba(var(--brz-global-color8), 1);
				background-image: none;
			}

			.brz .brz-css-1a7ogzm>.brz-bg>.brz-bg-map {
				display: none;
			}

			.brz .brz-css-1a7ogzm:hover>.brz-bg>.brz-bg-map {
				filter: none;
			}

			.brz .brz-css-1a7ogzm>.brz-bg>.brz-bg-video {
				display: none;
			}

			.brz .brz-css-1a7ogzm:hover>.brz-bg>.brz-bg-video {
				filter: none;
			}

			.brz .brz-css-1a7ogzm:hover>.brz-row {
				border: 0px solid transparent;
			}
		}

		@media (min-width:991px) {
			.brz .brz-css-1a7ogzm {
				min-height: auto;
				display: flex;
			}

			.brz .brz-css-1a7ogzm:hover>.brz-bg {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-1a7ogzm:hover>.brz-bg>.brz-bg-image {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-1a7ogzm:hover>.brz-bg>.brz-bg-color {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-1a7ogzm:hover>.brz-bg>.brz-bg-video {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-1a7ogzm:hover>.brz-row {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}
		}

		@media (max-width:991px) and (min-width:768px) {
			.brz .brz-css-1a7ogzm>.brz-bg {
				border-radius: 0px;
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-1a7ogzm>.brz-bg {
				border-radius: 0px;
			}
		}

		.brz .brz-css-lt4404 {
			padding: 10px;
			max-width: 100%;
		}

		@media (max-width:991px) and (min-width:768px) {
			.brz .brz-css-lt4404 {
				padding: 0;
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-lt4404 {
				padding: 0;
			}
		}

		.brz .brz-css-18oneyz {
			padding: 8px 40px 8px 40px;
		}

		@media (min-width:991px) {
			.brz .brz-css-18oneyz {
				padding: 8px 40px 8px 40px;
				max-width: 100%;
			}
		}

		@media (max-width:991px) and (min-width:768px) {
			.brz .brz-css-18oneyz {
				padding: 8px 32px 8px 24px;
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-18oneyz {
				padding: 8px 24px 8px 16px;
			}
		}

		.brz .brz-css-1csnpdv {
			z-index: auto;
			flex: 1 1 50%;
			max-width: 50%;
			justify-content: flex-start;
		}

		.brz .brz-css-1csnpdv .brz-columns__scroll-effect {
			justify-content: flex-start;
		}

		.brz .brz-css-1csnpdv>.brz-bg {
			margin: 0;
			mix-blend-mode: normal;
			border-radius: 0px;
		}

		.brz .brz-css-1csnpdv>.brz-bg {
			border: 0px solid rgba(102, 115, 141, 0);
			box-shadow: none;
		}

		.brz .brz-css-1csnpdv>.brz-bg>.brz-bg-image {
			background-size: cover;
			background-repeat: no-repeat;
			-webkit-mask-image: none;
			mask-image: none;
		}

		.brz .brz-css-1csnpdv>.brz-bg>.brz-bg-image {
			background-image: none;
			filter: none;
			display: block;
		}

		.brz .brz-css-1csnpdv>.brz-bg>.brz-bg-image:after {
			content: "";
			background-image: none;
		}

		.brz .brz-css-1csnpdv>.brz-bg>.brz-bg-color {
			-webkit-mask-image: none;
			mask-image: none;
		}

		.brz .brz-css-1csnpdv>.brz-bg>.brz-bg-color {
			background-color: rgba(0, 0, 0, 0);
			background-image: none;
		}

		.brz .brz-css-1csnpdv>.brz-bg>.brz-bg-map {
			display: none;
		}

		.brz .brz-css-1csnpdv>.brz-bg>.brz-bg-map {
			filter: none;
		}

		.brz .brz-css-1csnpdv>.brz-bg>.brz-bg-video {
			display: none;
		}

		.brz .brz-css-1csnpdv>.brz-bg>.brz-bg-video {
			filter: none;
		}

		@media (min-width:991px) {
			.brz .brz-css-1csnpdv>.brz-bg {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-1csnpdv>.brz-bg>.brz-bg-image {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-1csnpdv>.brz-bg>.brz-bg-color {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}
		}

		@media (max-width:991px) and (min-width:768px) {
			.brz .brz-css-1csnpdv>.brz-bg {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-1csnpdv>.brz-bg>.brz-bg-image {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-1csnpdv>.brz-bg>.brz-bg-color {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-1csnpdv {
				flex: 1 1 100%;
				max-width: 100%;
			}

			.brz .brz-css-1csnpdv>.brz-bg {
				margin: 10px 0px 10px 0px;
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-1csnpdv>.brz-bg {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-1csnpdv>.brz-bg>.brz-bg-image {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-1csnpdv>.brz-bg>.brz-bg-color {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}
		}

		.brz .brz-css-88dicl {
			flex: 1 1 26%;
			max-width: 26%;
			justify-content: center;
		}

		.brz .brz-css-88dicl .brz-columns__scroll-effect {
			justify-content: center;
		}

		.brz .brz-css-88dicl>.brz-bg {
			margin: 0;
		}

		@media (min-width:991px) {
			.brz .brz-css-88dicl {
				z-index: auto;
				flex: 1 1 26%;
				max-width: 26%;
				justify-content: center;
			}

			.brz .brz-css-88dicl .brz-columns__scroll-effect {
				justify-content: center;
			}

			.brz .brz-css-88dicl>.brz-bg {
				margin: 0;
				mix-blend-mode: normal;
				border-radius: 0px;
			}

			.brz .brz-css-88dicl:hover>.brz-bg {
				border: 0px solid rgba(102, 115, 141, 0);
				box-shadow: none;
			}

			.brz .brz-css-88dicl>.brz-bg>.brz-bg-image {
				background-size: cover;
				background-repeat: no-repeat;
				-webkit-mask-image: none;
				mask-image: none;
			}

			.brz .brz-css-88dicl:hover>.brz-bg>.brz-bg-image {
				background-image: none;
				filter: none;
				display: block;
			}

			.brz .brz-css-88dicl:hover>.brz-bg>.brz-bg-image:after {
				content: "";
				background-image: none;
			}

			.brz .brz-css-88dicl>.brz-bg>.brz-bg-color {
				-webkit-mask-image: none;
				mask-image: none;
			}

			.brz .brz-css-88dicl:hover>.brz-bg>.brz-bg-color {
				background-color: rgba(0, 0, 0, 0);
				background-image: none;
			}

			.brz .brz-css-88dicl>.brz-bg>.brz-bg-map {
				display: none;
			}

			.brz .brz-css-88dicl:hover>.brz-bg>.brz-bg-map {
				filter: none;
			}

			.brz .brz-css-88dicl>.brz-bg>.brz-bg-video {
				display: none;
			}

			.brz .brz-css-88dicl:hover>.brz-bg>.brz-bg-video {
				filter: none;
			}
		}

		@media (min-width:991px) {
			.brz .brz-css-88dicl:hover>.brz-bg {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-88dicl:hover>.brz-bg>.brz-bg-image {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-88dicl:hover>.brz-bg>.brz-bg-color {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}
		}

		@media (max-width:991px) and (min-width:768px) {
			.brz .brz-css-88dicl {
				flex: 1 1 37.3%;
				max-width: 37.3%;
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-88dicl {
				flex: 1 1 60%;
				max-width: 60%;
			}
		}

		.brz .brz-css-1pzte4r {
			z-index: auto;
			margin: 0;
			border: 0px solid transparent;
			padding: 5px 15px 5px 15px;
			min-height: 100%;
		}

		@media (min-width:991px) {
			.brz .brz-css-1pzte4r {
				display: flex;
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}
		}

		@media (max-width:991px) and (min-width:768px) {
			.brz .brz-css-1pzte4r {
				display: flex;
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-1pzte4r {
				margin: 10px 0px 10px 0px;
				padding: 0;
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-1pzte4r {
				display: flex;
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}
		}

		.brz .brz-css-znagm0 {
			margin: 0;
			padding: 0;
		}

		@media (min-width:991px) {
			.brz .brz-css-znagm0 {
				z-index: auto;
				margin: 0;
				border: 0px solid transparent;
				padding: 0;
				min-height: 100%;
			}
		}

		@media (min-width:991px) {
			.brz .brz-css-znagm0:hover {
				display: flex;
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}
		}

		.brz .brz-css-1tjbyob {
			padding: 0;
			margin: 10px 0px 10px 0px;
			justify-content: center;
			position: relative;
		}

		.brz .brz-css-1tjbyob .brz-wrapper-transform {
			transform: none;
		}

		@media (min-width:991px) {
			.brz .brz-css-1tjbyob {
				display: flex;
				z-index: auto;
				position: relative;
			}
		}

		@media (max-width:991px) and (min-width:768px) {
			.brz .brz-css-1tjbyob {
				display: flex;
				z-index: auto;
				position: relative;
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-1tjbyob {
				display: flex;
				z-index: auto;
				position: relative;
			}
		}

		.brz .brz-css-1puqgto {
			margin: 0;
			justify-content: flex-start;
		}

		@media (min-width:991px) {
			.brz .brz-css-1puqgto {
				padding: 0;
				margin: 0;
				justify-content: flex-start;
				position: relative;
			}

			.brz .brz-css-1puqgto .brz-wrapper-transform {
				transform: none;
			}
		}

		@media (min-width:991px) {
			.brz .brz-css-1puqgto {
				display: flex;
				z-index: auto;
				position: relative;
			}
		}

		.brz .brz-css-q31jzm:not(.brz-image--hovered) {
			max-width: 100%;
		}

		.brz .brz-css-q31jzm {
			height: auto;
			border-radius: 0px;
			mix-blend-mode: normal;
		}

		.brz .brz-css-q31jzm {
			box-shadow: none;
			border: 0px solid rgba(102, 115, 141, 0);
		}

		.brz .brz-css-q31jzm .brz-picture:after {
			border-radius: 0px;
		}

		.brz .brz-css-q31jzm .brz-picture:after {
			box-shadow: none;
			background-color: rgba(255, 255, 255, 0);
			background-image: none;
		}

		.brz .brz-css-q31jzm .brz-picture {
			-webkit-mask-image: none;
			mask-image: none;
		}

		.brz .brz-css-q31jzm .brz-picture {
			filter: none;
		}

		@media (min-width:991px) {
			.brz .brz-css-q31jzm {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-q31jzm .brz-picture:after {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-q31jzm .brz-picture {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-q31jzm.brz-image--withHover img.brz-img,
			.brz .brz-css-q31jzm.brz-image--withHover img.dynamic-image,
			.brz .brz-css-q31jzm.brz-image--withHover .brz-img__hover {
				transition-duration: .5s;
			}
		}

		@media (max-width:991px) and (min-width:768px) {
			.brz .brz-css-q31jzm {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-q31jzm .brz-picture:after {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-q31jzm .brz-picture {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-q31jzm.brz-image--withHover img.brz-img,
			.brz .brz-css-q31jzm.brz-image--withHover img.dynamic-image,
			.brz .brz-css-q31jzm.brz-image--withHover .brz-img__hover {
				transition-duration: .5s;
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-q31jzm {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-q31jzm .brz-picture:after {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-q31jzm .brz-picture {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-q31jzm.brz-image--withHover img.brz-img,
			.brz .brz-css-q31jzm.brz-image--withHover img.dynamic-image,
			.brz .brz-css-q31jzm.brz-image--withHover .brz-img__hover {
				transition-duration: .5s;
			}
		}

		@media (min-width:991px) {
			.brz .brz-css-yp9xav:not(.brz-image--hovered) {
				max-width: 100%;
			}

			.brz .brz-css-yp9xav {
				height: auto;
				border-radius: 0px;
				mix-blend-mode: normal;
			}

			.brz .brz-css-yp9xav:hover {
				box-shadow: none;
				border: 0px solid rgba(102, 115, 141, 0);
			}

			.brz .brz-css-yp9xav .brz-picture:after {
				border-radius: 0px;
			}

			.brz .brz-css-yp9xav:hover .brz-picture:after {
				box-shadow: none;
				background-color: rgba(255, 255, 255, 0);
				background-image: none;
			}

			.brz .brz-css-yp9xav .brz-picture {
				-webkit-mask-image: none;
				mask-image: none;
			}

			.brz .brz-css-yp9xav:hover .brz-picture {
				filter: none;
			}
		}

		@media (min-width:991px) {
			.brz .brz-css-yp9xav:hover {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-yp9xav:hover .brz-picture:after {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-yp9xav:hover .brz-picture {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-yp9xav.brz-image--withHover img.brz-img,
			.brz .brz-css-yp9xav.brz-image--withHover img.dynamic-image,
			.brz .brz-css-yp9xav.brz-image--withHover .brz-img__hover {
				transition-duration: .5s;
			}
		}

		.brz .brz-css-h68n6u.brz-hover-animation__container {
			max-width: 100%;
		}

		@media (min-width:991px) {
			.brz .brz-css-ypzqsr.brz-hover-animation__container {
				max-width: 100%;
			}
		}

		.brz .brz-css-1yykq65 {
			padding-top: 28.8003%;
		}

		.brz .brz-css-1yykq65>.brz-img {
			position: absolute;
			width: 100%;
		}

		@media (max-width:991px) and (min-width:768px) {
			.brz .brz-css-1yykq65 {
				padding-top: 28.7989%;
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-1yykq65 {
				padding-top: 28.7991%;
			}
		}

		.brz .brz-css-z4zlrs {
			padding-top: 0;
		}

		.brz .brz-css-z4zlrs>.brz-img {
			position: inherit;
		}

		@media (min-width:991px) {
			.brz .brz-css-z4zlrs {
				padding-top: 0;
			}

			.brz .brz-css-z4zlrs>.brz-img {
				position: inherit;
				width: 100%;
			}
		}

		.brz .brz-css-uk9miq {
			width: 378.89px;
			height: 109.12px;
			margin-left: -81.75px;
			margin-top: 0px;
		}

		@media (max-width:991px) and (min-width:768px) {
			.brz .brz-css-uk9miq {
				width: 299.03px;
				height: 86.12px;
				margin-left: -64.52px;
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-uk9miq {
				width: 329.31px;
				height: 94.84px;
				margin-left: -71.05px;
			}
		}

		.brz .brz-css-vw7hrb {
			width: 100%;
			height: auto;
			margin-left: auto;
			margin-top: auto;
		}

		@media (min-width:991px) {
			.brz .brz-css-vw7hrb {
				width: 100%;
				height: auto;
				margin-left: auto;
				margin-top: auto;
			}
		}

		.brz .brz-css-1t2tmqk {
			flex: 1 1 67.3%;
			max-width: 67.3%;
			justify-content: center;
		}

		.brz .brz-css-1t2tmqk .brz-columns__scroll-effect {
			justify-content: center;
		}

		.brz .brz-css-1t2tmqk>.brz-bg {
			margin: 0px 32px 0px 0px;
		}

		@media (min-width:991px) {
			.brz .brz-css-1t2tmqk {
				z-index: auto;
				flex: 1 1 67.3%;
				max-width: 67.3%;
				justify-content: center;
			}

			.brz .brz-css-1t2tmqk .brz-columns__scroll-effect {
				justify-content: center;
			}

			.brz .brz-css-1t2tmqk>.brz-bg {
				margin: 0px 32px 0px 0px;
				mix-blend-mode: normal;
				border-radius: 0px;
			}

			.brz .brz-css-1t2tmqk:hover>.brz-bg {
				border: 0px solid rgba(102, 115, 141, 0);
				box-shadow: none;
			}

			.brz .brz-css-1t2tmqk>.brz-bg>.brz-bg-image {
				background-size: cover;
				background-repeat: no-repeat;
				-webkit-mask-image: none;
				mask-image: none;
			}

			.brz .brz-css-1t2tmqk:hover>.brz-bg>.brz-bg-image {
				background-image: none;
				filter: none;
				display: block;
			}

			.brz .brz-css-1t2tmqk:hover>.brz-bg>.brz-bg-image:after {
				content: "";
				background-image: none;
			}

			.brz .brz-css-1t2tmqk>.brz-bg>.brz-bg-color {
				-webkit-mask-image: none;
				mask-image: none;
			}

			.brz .brz-css-1t2tmqk:hover>.brz-bg>.brz-bg-color {
				background-color: rgba(0, 0, 0, 0);
				background-image: none;
			}

			.brz .brz-css-1t2tmqk>.brz-bg>.brz-bg-map {
				display: none;
			}

			.brz .brz-css-1t2tmqk:hover>.brz-bg>.brz-bg-map {
				filter: none;
			}

			.brz .brz-css-1t2tmqk>.brz-bg>.brz-bg-video {
				display: none;
			}

			.brz .brz-css-1t2tmqk:hover>.brz-bg>.brz-bg-video {
				filter: none;
			}
		}

		@media (min-width:991px) {
			.brz .brz-css-1t2tmqk:hover>.brz-bg {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-1t2tmqk:hover>.brz-bg>.brz-bg-image {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-1t2tmqk:hover>.brz-bg>.brz-bg-color {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}
		}

		@media (max-width:991px) and (min-width:768px) {
			.brz .brz-css-1t2tmqk {
				flex: 1 1 61.3%;
				max-width: 61.3%;
			}

			.brz .brz-css-1t2tmqk>.brz-bg {
				margin: 0;
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-1t2tmqk {
				flex: 1 1 40%;
				max-width: 40%;
			}

			.brz .brz-css-1t2tmqk>.brz-bg {
				margin: 0;
			}
		}

		.brz .brz-css-kmg040 {
			margin: 0px 32px 0px 0px;
			padding: 0px 0px 0px 16px;
		}

		@media (min-width:991px) {
			.brz .brz-css-kmg040 {
				z-index: auto;
				margin: 0px 32px 0px 0px;
				border: 0px solid transparent;
				padding: 0px 0px 0px 16px;
				min-height: 100%;
			}
		}

		@media (min-width:991px) {
			.brz .brz-css-kmg040:hover {
				display: flex;
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}
		}

		@media (max-width:991px) and (min-width:768px) {
			.brz .brz-css-kmg040 {
				margin: 0;
				padding: 0px 0px 0px 5px;
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-kmg040 {
				margin: 0;
				padding: 0px 8px 0px 0px;
			}
		}

		.brz .brz-css-icg1tg {
			padding: 8px;
			margin: 8px 0px 8px 0px;
			justify-content: flex-end;
		}

		@media (min-width:991px) {
			.brz .brz-css-icg1tg {
				padding: 8px;
				margin: 8px 0px 8px 0px;
				justify-content: flex-end;
				position: relative;
			}

			.brz .brz-css-icg1tg .brz-wrapper-transform {
				transform: none;
			}
		}

		@media (min-width:991px) {
			.brz .brz-css-icg1tg {
				display: flex;
				z-index: auto;
				position: relative;
			}
		}

		@media (max-width:991px) and (min-width:768px) {
			.brz .brz-css-icg1tg {
				padding: 0;
				margin: 5px 0px 5px 0px;
			}
		}

		@media (max-width:991px) and (min-width:768px) {
			.brz .brz-css-icg1tg {
				display: none;
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-icg1tg {
				padding: 0;
				margin: 10px 0px 10px 0px;
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-icg1tg {
				display: none;
			}
		}

		@media (min-width:991px) {
			.brz .brz-css-19uzdaw .brz-mm-menu__icon {
				display: none;
				font-size: 18px;
			}

			.brz .brz-css-19uzdaw .brz-mm-menu__icon {
				color: rgba(51, 51, 51, 1);
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-19uzdaw .brz-menu {
				display: flex;
			}
		}

		@media (max-width:991px) and (min-width:768px) {
			.brz .brz-css-19uzdaw .brz-mm-menu__icon {
				display: flex;
				font-size: 18px;
			}

			.brz .brz-css-19uzdaw .brz-mm-menu__icon {
				color: rgba(51, 51, 51, 1);
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-19uzdaw .brz-menu {
				display: none;
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-19uzdaw .brz-mm-menu__icon {
				display: flex;
				font-size: 18px;
			}

			.brz .brz-css-19uzdaw .brz-mm-menu__icon {
				color: rgba(51, 51, 51, 1);
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-19uzdaw .brz-menu {
				display: none;
			}
		}

		@media (min-width:991px) {
			.brz .brz-css-159oqwi .brz-mm-menu__icon {
				font-size: 18px;
			}

			.brz .brz-css-159oqwi .brz-mm-menu__icon {
				color: rgba(51, 51, 51, 1);
			}
		}

		@media (min-width:991px) {
			.brz .brz-css-159oqwi .brz-mm-menu__icon {
				display: none;
				font-size: 18px;
			}

			.brz .brz-css-159oqwi:hover .brz-mm-menu__icon {
				color: rgba(51, 51, 51, 1);
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-159oqwi .brz-menu {
				display: flex;
			}
		}

		@media (max-width:991px) and (min-width:768px) {
			.brz .brz-css-159oqwi .brz-mm-menu__icon {
				font-size: 38px;
			}

			.brz .brz-css-159oqwi .brz-mm-menu__icon {
				color: rgba(var(--brz-global-color7), 1);
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-159oqwi .brz-mm-menu__icon {
				font-size: 32px;
			}

			.brz .brz-css-159oqwi .brz-mm-menu__icon {
				color: rgba(var(--brz-global-color7), 1);
			}
		}

		.brz .brz-css-c39cel .brz-menu__ul {
			font-family: var(--brz-buttonfontfamily, initial);
			display: flex;
			flex-wrap: wrap;
			justify-content: inherit;
			align-items: center;
			max-width: none;
			margin: 0px -5px 0px -5px;
		}

		.brz .brz-css-c39cel .brz-menu__ul {
			color: rgba(0, 0, 0, 1);
		}

		.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item>.brz-a {
			flex-flow: row nowrap;
			padding: 0px 5px 0px 5px;
		}

		.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item>.brz-a {
			color: rgba(0, 0, 0, 1);
			background-color: rgba(255, 255, 255, 0);
		}

		.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item.brz-menu__item--opened>.brz-a {
			color: rgba(0, 0, 0, 1);
			background-color: rgba(255, 255, 255, 0);
		}

		.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item.brz-menu__item--opened {
			color: rgba(0, 0, 0, 1);
			background-color: transparent;
			border: 0px solid rgba(85, 85, 85, 1);
		}

		.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {
			color: rgba(0, 0, 0, 1);
			background-color: rgba(255, 255, 255, 0);
			border: 0px solid rgba(85, 85, 85, 1);
		}

		.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item.brz-menu__item--current>.brz-a:not(.brz-a:active) {
			color: rgba(0, 0, 0, 1);
			background-color: rgba(255, 255, 255, 0);
		}

		.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item>.brz-a>.brz-icon-svg {
			margin: 0 15px 0 0;
		}

		.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item>.brz-a>.brz-icon-svg.brz-icon-svg-custom {
			background-color: rgba(0, 0, 0, 1);
		}

		.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item.brz-menu__item--current>.brz-a:not(.brz-a:active)>.brz-icon-svg.brz-icon-svg-custom {
			background-color: rgba(0, 0, 0, 1);
		}

		.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active)>.brz-icon-svg.brz-icon-svg-custom {
			background-color: rgba(0, 0, 0, 1);
		}

		.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item {
			border-radius: 0px;
		}

		.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item {
			color: rgba(0, 0, 0, 1);
			background-color: transparent;
			border: 0px solid rgba(85, 85, 85, 1);
		}

		.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item>a {
			border-radius: 0px;
		}

		.brz .brz-css-c39cel .brz-menu__sub-menu {
			font-family: var(--brz-buttonfontfamily, initial);
			border-radius: 0px;
		}

		.brz .brz-css-c39cel .brz-menu__sub-menu {
			color: rgba(255, 255, 255, 1);
			background-color: rgba(51, 51, 51, 1);
			box-shadow: none;
		}

		.brz .brz-css-c39cel .brz-menu__sub-menu .brz-menu__item>.brz-a {
			flex-flow: row nowrap;
		}

		.brz .brz-css-c39cel .brz-menu__sub-menu .brz-a:hover {
			color: rgba(255, 255, 255, 1);
		}

		.brz .brz-css-c39cel .brz-menu__sub-menu .brz-a>.brz-icon-svg {
			margin: 0 15px 0 0;
			font-size: 12px;
		}

		.brz .brz-css-c39cel .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current>.brz-a {
			color: rgba(255, 255, 255, 1);
		}

		.brz .brz-css-c39cel .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current {
			background-color: rgba(51, 51, 51, 1);
		}

		.brz .brz-css-c39cel .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current:hover {
			background-color: rgba(51, 51, 51, 1);
		}

		.brz .brz-css-c39cel .brz-menu__item--current .brz-menu__sub-menu {
			box-shadow: none;
		}

		.brz .brz-css-c39cel .brz-menu__sub-menu>.brz-menu__item>.brz-a>.brz-icon-svg.brz-icon-svg-custom {
			background-color: rgba(255, 255, 255, 1);
		}

		.brz .brz-css-c39cel .brz-menu__sub-menu>.brz-menu__item:not(.brz-menu__item.brz-menu__item--current)>.brz-a>.brz-icon-svg.brz-icon-svg-custom {
			background-color: rgba(255, 255, 255, 1);
		}

		.brz .brz-css-c39cel .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current>.brz-a>.brz-icon-svg.brz-icon-svg-custom {
			background-color: rgba(255, 255, 255, 1);
		}

		.brz .brz-css-c39cel .brz-menu__item-dropdown .brz-menu__item {
			background-color: rgba(51, 51, 51, 1);
			color: rgba(255, 255, 255, 1);
		}

		.brz .brz-css-c39cel .brz-menu__sub-menu .brz-menu__item-dropdown .brz-a:hover:after {
			border-color: rgba(255, 255, 255, 1);
		}

		.brz .brz-css-c39cel .brz-menu__sub-menu>.brz-menu__item {
			border-bottom: 1px solid rgba(85, 85, 85, 1);
		}

		@media (min-width:991px) {
			.brz .brz-css-c39cel .brz-menu__ul {
				font-size: var(--brz-buttonfontsize, initial);
				font-weight: var(--brz-buttonfontweight, initial);
				font-weight: var(--brz-buttonbold, initial);
				line-height: var(--brz-buttonlineheight, initial);
				letter-spacing: var(--brz-buttonletterspacing, initial);
				font-variation-settings: var(--brz-buttonfontvariation, initial);
				font-style: var(--brz-buttonitalic, initial);
				text-decoration: var(--brz-buttontextdecoration, initial) !important;
				text-transform: var(--brz-buttontexttransform, initial) !important;
			}

			.brz .brz-css-c39cel .brz-menu__ul {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item>.brz-a {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item.brz-menu__item--opened>.brz-a {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item.brz-menu__item--opened {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item.brz-menu__item--current>.brz-a:not(.brz-a:active) {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item>.brz-a>.brz-icon-svg {
				font-size: 12px;
			}

			.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item>.brz-a>.brz-icon-svg.brz-icon-svg-custom {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item.brz-menu__item--current>.brz-a:not(.brz-a:active)>.brz-icon-svg.brz-icon-svg-custom {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active)>.brz-icon-svg.brz-icon-svg-custom {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item {
				padding-top: 0px;
				padding-bottom: 0px;
				margin-right: 5px;
				margin-left: 5px;
			}

			.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__sub-menu {
				font-size: var(--brz-buttonfontsize, initial);
				font-weight: var(--brz-buttonfontweight, initial);
				font-weight: var(--brz-buttonbold, initial);
				line-height: var(--brz-buttonlineheight, initial);
				letter-spacing: var(--brz-buttonletterspacing, initial);
				font-variation-settings: var(--brz-buttonfontvariation, initial);
				font-style: var(--brz-buttonitalic, initial);
				text-decoration: var(--brz-buttontextdecoration, initial) !important;
				text-transform: var(--brz-buttontexttransform, initial) !important;
				position: absolute;
				top: 0;
				width: 305px;
			}

			.brz .brz-css-c39cel .brz-menu__sub-menu {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__sub-menu .brz-a:hover {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current>.brz-a {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current:hover {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__item--current .brz-menu__sub-menu {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__sub-menu>.brz-menu__item>.brz-a>.brz-icon-svg.brz-icon-svg-custom {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__sub-menu>.brz-menu__item:not(.brz-menu__item.brz-menu__item--current)>.brz-a>.brz-icon-svg.brz-icon-svg-custom {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current>.brz-a>.brz-icon-svg.brz-icon-svg-custom {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__item-dropdown .brz-menu__item {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__sub-menu .brz-menu__item-dropdown .brz-a:hover:after {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel [data-popper-placement='left-start'] {
				right: calc(100% + 5px);
			}

			.brz .brz-css-c39cel [data-popper-placement='right-start'] {
				left: calc(100% + 5px);
			}

			.brz .brz-css-c39cel>.brz-menu__ul>.brz-menu__item-dropdown>.brz-menu__sub-menu {
				top: calc(100% + 5px);
				width: 300px;
			}

			.brz .brz-css-c39cel>.brz-menu__ul>.brz-menu__item-dropdown>[data-popper-placement='left-start'] {
				right: 0;
			}

			.brz .brz-css-c39cel>.brz-menu__ul>.brz-menu__item-dropdown>[data-popper-placement='right-start'] {
				left: 0;
			}

			.brz .brz-css-c39cel .brz-mega-menu__dropdown {
				display: none;
			}
		}

		@media (max-width:991px) and (min-width:768px) {
			.brz .brz-css-c39cel .brz-menu__ul {
				font-size: var(--brz-buttontabletfontsize, initial);
				font-weight: var(--brz-buttontabletfontweight, initial);
				font-weight: var(--brz-buttontabletbold, initial);
				line-height: var(--brz-buttontabletlineheight, initial);
				letter-spacing: var(--brz-buttontabletletterspacing, initial);
				font-variation-settings: var(--brz-buttontabletfontvariation, initial);
				font-style: var(--brz-buttontabletitalic, initial);
				text-decoration: var(--brz-buttontablettextdecoration, initial) !important;
				text-transform: var(--brz-buttontablettexttransform, initial) !important;
			}

			.brz .brz-css-c39cel .brz-menu__ul {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item>.brz-a {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item.brz-menu__item--opened>.brz-a {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item.brz-menu__item--opened {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item.brz-menu__item--current>.brz-a:not(.brz-a:active) {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item>.brz-a>.brz-icon-svg {
				font-size: 12px;
			}

			.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item>.brz-a>.brz-icon-svg.brz-icon-svg-custom {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item.brz-menu__item--current>.brz-a:not(.brz-a:active)>.brz-icon-svg.brz-icon-svg-custom {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active)>.brz-icon-svg.brz-icon-svg-custom {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item {
				padding-top: 0px;
				padding-bottom: 0px;
				margin-right: 5px;
				margin-left: 5px;
			}

			.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__sub-menu {
				font-size: var(--brz-buttontabletfontsize, initial);
				font-weight: var(--brz-buttontabletfontweight, initial);
				font-weight: var(--brz-buttontabletbold, initial);
				line-height: var(--brz-buttontabletlineheight, initial);
				letter-spacing: var(--brz-buttontabletletterspacing, initial);
				font-variation-settings: var(--brz-buttontabletfontvariation, initial);
				font-style: var(--brz-buttontabletitalic, initial);
				text-decoration: var(--brz-buttontablettextdecoration, initial) !important;
				text-transform: var(--brz-buttontablettexttransform, initial) !important;
				position: absolute;
				top: 0;
				width: 305px;
			}

			.brz .brz-css-c39cel .brz-menu__sub-menu {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__sub-menu .brz-a:hover {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current>.brz-a {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current:hover {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__item--current .brz-menu__sub-menu {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__sub-menu>.brz-menu__item>.brz-a>.brz-icon-svg.brz-icon-svg-custom {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__sub-menu>.brz-menu__item:not(.brz-menu__item.brz-menu__item--current)>.brz-a>.brz-icon-svg.brz-icon-svg-custom {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current>.brz-a>.brz-icon-svg.brz-icon-svg-custom {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__item-dropdown .brz-menu__item {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__sub-menu .brz-menu__item-dropdown .brz-a:hover:after {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel>.brz-menu__ul>.brz-menu__item-dropdown>.brz-menu__sub-menu {
				top: calc(100% + 5px);
				width: 300px;
			}

			.brz .brz-css-c39cel>.brz-menu__ul>.brz-menu__item-dropdown>[data-popper-placement='left-start'] {
				right: 0;
			}

			.brz .brz-css-c39cel>.brz-menu__ul>.brz-menu__item-dropdown>[data-popper-placement='right-start'] {
				left: 0;
			}

			.brz .brz-css-c39cel .brz-mega-menu__dropdown {
				display: none;
			}

			.brz .brz-css-c39cel .brz-menu__sub-menu .brz-menu__item-dropdown>.brz-a:after {
				border-right-style: solid;
				border-left-style: none;
			}

			.brz .brz-css-c39cel .brz-menu__sub-menu .brz-menu__item-dropdown .brz-menu__sub-menu {
				position: relative;
				top: auto;
				left: auto;
				transform: translate(0, 0);
				height: 0;
				overflow: hidden;
			}

			.brz .brz-css-c39cel .brz-menu__sub-menu .brz-menu__item--opened>.brz-menu__sub-menu {
				height: auto;
				width: 100%;
				left: auto;
				right: auto;
			}

			.brz .brz-css-c39cel.brz-menu__preview .brz-menu__sub-menu .brz-menu__item>.brz-menu__sub-menu {
				height: auto;
				width: 100%;
				left: auto;
				right: auto;
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-c39cel .brz-menu__ul {
				font-size: var(--brz-buttonmobilefontsize, initial);
				font-weight: var(--brz-buttonmobilefontweight, initial);
				font-weight: var(--brz-buttonmobilebold, initial);
				line-height: var(--brz-buttonmobilelineheight, initial);
				letter-spacing: var(--brz-buttonmobileletterspacing, initial);
				font-variation-settings: var(--brz-buttonmobilefontvariation, initial);
				font-style: var(--brz-buttonmobileitalic, initial);
				text-decoration: var(--brz-buttonmobiletextdecoration, initial) !important;
				text-transform: var(--brz-buttonmobiletexttransform, initial) !important;
			}

			.brz .brz-css-c39cel .brz-menu__ul {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item>.brz-a {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item.brz-menu__item--opened>.brz-a {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item.brz-menu__item--opened {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item.brz-menu__item--current>.brz-a:not(.brz-a:active) {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item>.brz-a>.brz-icon-svg {
				font-size: 12px;
			}

			.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item>.brz-a>.brz-icon-svg.brz-icon-svg-custom {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item.brz-menu__item--current>.brz-a:not(.brz-a:active)>.brz-icon-svg.brz-icon-svg-custom {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active)>.brz-icon-svg.brz-icon-svg-custom {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item {
				padding-top: 0px;
				padding-bottom: 0px;
				margin-right: 5px;
				margin-left: 5px;
			}

			.brz .brz-css-c39cel .brz-menu__ul>.brz-menu__item {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__sub-menu {
				font-size: var(--brz-buttonmobilefontsize, initial);
				font-weight: var(--brz-buttonmobilefontweight, initial);
				font-weight: var(--brz-buttonmobilebold, initial);
				line-height: var(--brz-buttonmobilelineheight, initial);
				letter-spacing: var(--brz-buttonmobileletterspacing, initial);
				font-variation-settings: var(--brz-buttonmobilefontvariation, initial);
				font-style: var(--brz-buttonmobileitalic, initial);
				text-decoration: var(--brz-buttonmobiletextdecoration, initial) !important;
				text-transform: var(--brz-buttonmobiletexttransform, initial) !important;
				position: absolute;
				top: 0;
				width: 305px;
			}

			.brz .brz-css-c39cel .brz-menu__sub-menu {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__sub-menu .brz-a:hover {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current>.brz-a {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current:hover {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__item--current .brz-menu__sub-menu {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__sub-menu>.brz-menu__item>.brz-a>.brz-icon-svg.brz-icon-svg-custom {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__sub-menu>.brz-menu__item:not(.brz-menu__item.brz-menu__item--current)>.brz-a>.brz-icon-svg.brz-icon-svg-custom {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current>.brz-a>.brz-icon-svg.brz-icon-svg-custom {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__item-dropdown .brz-menu__item {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel .brz-menu__sub-menu .brz-menu__item-dropdown .brz-a:hover:after {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-c39cel>.brz-menu__ul>.brz-menu__item-dropdown>.brz-menu__sub-menu {
				top: calc(100% + 5px);
				width: 300px;
			}

			.brz .brz-css-c39cel>.brz-menu__ul>.brz-menu__item-dropdown>[data-popper-placement='left-start'] {
				right: 0;
			}

			.brz .brz-css-c39cel>.brz-menu__ul>.brz-menu__item-dropdown>[data-popper-placement='right-start'] {
				left: 0;
			}

			.brz .brz-css-c39cel .brz-mega-menu__dropdown {
				display: block;
			}

			.brz .brz-css-c39cel .brz-menu__sub-menu .brz-menu__item-dropdown>.brz-a:after {
				border-right-style: solid;
				border-left-style: none;
			}

			.brz .brz-css-c39cel .brz-menu__sub-menu .brz-menu__item-dropdown .brz-menu__sub-menu {
				position: relative;
				top: auto;
				left: auto;
				transform: translate(0, 0);
				height: 0;
				overflow: hidden;
			}

			.brz .brz-css-c39cel .brz-menu__sub-menu .brz-menu__item--opened>.brz-menu__sub-menu {
				height: auto;
				width: 100%;
				left: auto;
				right: auto;
			}

			.brz .brz-css-c39cel.brz-menu__preview .brz-menu__sub-menu .brz-menu__item>.brz-menu__sub-menu {
				height: auto;
				width: 100%;
				left: auto;
				right: auto;
			}
		}

		.brz .brz-css-1tdvdmo .brz-menu__ul {
			font-family: var(--brz-ugudlcdcxlbqfontfamily, initial);
			margin: 0px -17.5px 0px -17.5px;
		}

		.brz .brz-css-1tdvdmo .brz-menu__ul {
			color: rgba(var(--brz-global-color7), 1);
		}

		.brz .brz-css-1tdvdmo .brz-menu__ul>.brz-menu__item>.brz-a {
			color: rgba(var(--brz-global-color7), 1);
		}

		.brz .brz-css-1tdvdmo .brz-menu__ul>.brz-menu__item.brz-menu__item--opened>.brz-a {
			color: rgba(var(--brz-global-color7), 1);
		}

		.brz .brz-css-1tdvdmo .brz-menu__ul>.brz-menu__item.brz-menu__item--opened {
			color: rgba(var(--brz-global-color7), 1);
		}

		.brz .brz-css-1tdvdmo .brz-menu__ul>.brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {
			color: rgba(var(--brz-global-color2), 1);
		}

		.brz .brz-css-1tdvdmo .brz-menu__ul>.brz-menu__item.brz-menu__item--current>.brz-a:not(.brz-a:active) {
			color: rgba(var(--brz-global-color2), 1);
		}

		.brz .brz-css-1tdvdmo .brz-menu__ul>.brz-menu__item>.brz-a>.brz-icon-svg.brz-icon-svg-custom {
			background-color: rgba(var(--brz-global-color7), 1);
		}

		.brz .brz-css-1tdvdmo .brz-menu__ul>.brz-menu__item.brz-menu__item--current>.brz-a:not(.brz-a:active)>.brz-icon-svg.brz-icon-svg-custom {
			background-color: rgba(var(--brz-global-color2), 1);
		}

		.brz .brz-css-1tdvdmo .brz-menu__ul>.brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active)>.brz-icon-svg.brz-icon-svg-custom {
			background-color: rgba(var(--brz-global-color2), 1);
		}

		.brz .brz-css-1tdvdmo .brz-menu__ul>.brz-menu__item {
			color: rgba(var(--brz-global-color7), 1);
		}

		.brz .brz-css-1tdvdmo .brz-menu__sub-menu {
			font-family: var(--brz-heading5fontfamily, initial);
		}

		.brz .brz-css-1tdvdmo .brz-menu__sub-menu {
			color: rgba(164, 248, 240, 1);
			background-color: rgba(var(--brz-global-color3), 1);
		}

		.brz .brz-css-1tdvdmo .brz-menu__sub-menu .brz-a:hover {
			color: rgba(164, 248, 240, 1);
		}

		.brz .brz-css-1tdvdmo .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current>.brz-a {
			color: rgba(var(--brz-global-color8), 1);
		}

		.brz .brz-css-1tdvdmo .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current {
			background-color: rgba(var(--brz-global-color3), 1);
		}

		.brz .brz-css-1tdvdmo .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current:hover {
			background-color: rgba(var(--brz-global-color3), 1);
		}

		.brz .brz-css-1tdvdmo .brz-menu__sub-menu>.brz-menu__item>.brz-a>.brz-icon-svg.brz-icon-svg-custom {
			background-color: rgba(164, 248, 240, 1);
		}

		.brz .brz-css-1tdvdmo .brz-menu__sub-menu>.brz-menu__item:not(.brz-menu__item.brz-menu__item--current)>.brz-a>.brz-icon-svg.brz-icon-svg-custom {
			background-color: rgba(164, 248, 240, 1);
		}

		.brz .brz-css-1tdvdmo .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current>.brz-a>.brz-icon-svg.brz-icon-svg-custom {
			background-color: rgba(var(--brz-global-color8), 1);
		}

		.brz .brz-css-1tdvdmo .brz-menu__item-dropdown .brz-menu__item {
			background-color: rgba(var(--brz-global-color3), 1);
			color: rgba(164, 248, 240, 1);
		}

		.brz .brz-css-1tdvdmo .brz-menu__sub-menu .brz-menu__item-dropdown .brz-a:hover:after {
			border-color: rgba(164, 248, 240, 1);
		}

		@media (min-width:991px) {
			.brz .brz-css-1tdvdmo .brz-menu__ul {
				font-size: var(--brz-ugudlcdcxlbqfontsize, initial);
				font-weight: var(--brz-ugudlcdcxlbqfontweight, initial);
				font-weight: var(--brz-ugudlcdcxlbqbold, initial);
				line-height: var(--brz-ugudlcdcxlbqlineheight, initial);
				letter-spacing: var(--brz-ugudlcdcxlbqletterspacing, initial);
				font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation, initial);
				font-style: var(--brz-ugudlcdcxlbqitalic, initial);
				text-decoration: var(--brz-ugudlcdcxlbqtextdecoration, initial) !important;
				text-transform: var(--brz-ugudlcdcxlbqtexttransform, initial) !important;
			}

			.brz .brz-css-1tdvdmo .brz-menu__ul>.brz-menu__item {
				padding-top: 0px;
				padding-bottom: 0px;
				margin-right: 17.5px;
				margin-left: 17.5px;
			}

			.brz .brz-css-1tdvdmo .brz-menu__sub-menu {
				font-size: var(--brz-heading5fontsize, initial);
				font-weight: var(--brz-heading5fontweight, initial);
				font-weight: var(--brz-heading5bold, initial);
				line-height: var(--brz-heading5lineheight, initial);
				letter-spacing: var(--brz-heading5letterspacing, initial);
				font-variation-settings: var(--brz-heading5fontvariation, initial);
				font-style: var(--brz-heading5italic, initial);
				text-decoration: var(--brz-heading5textdecoration, initial) !important;
				text-transform: var(--brz-heading5texttransform, initial) !important;
			}
		}

		@media (min-width:991px) {
			.brz .brz-css-1tdvdmo .brz-menu__ul {
				font-family: var(--brz-ugudlcdcxlbqfontfamily, initial);
				display: flex;
				flex-wrap: wrap;
				justify-content: inherit;
				align-items: center;
				max-width: none;
				margin: 0px -17.5px 0px -17.5px;
			}

			.brz .brz-css-1tdvdmo:hover .brz-menu__ul {
				color: rgba(var(--brz-global-color2), 1);
			}

			.brz .brz-css-1tdvdmo .brz-menu__ul>.brz-menu__item>.brz-a {
				flex-flow: row nowrap;
				padding: 0px 5px 0px 5px;
			}

			.brz .brz-css-1tdvdmo .brz-menu__ul>.brz-menu__item>.brz-a:hover {
				color: rgba(var(--brz-global-color2), 1);
				background-color: rgba(255, 255, 255, 0);
			}

			.brz .brz-css-1tdvdmo .brz-menu__ul>.brz-menu__item.brz-menu__item--opened>.brz-a:hover {
				color: rgba(var(--brz-global-color2), 1);
				background-color: rgba(255, 255, 255, 0);
			}

			.brz .brz-css-1tdvdmo .brz-menu__ul>.brz-menu__item.brz-menu__item--opened:hover {
				color: rgba(var(--brz-global-color2), 1);
				background-color: transparent;
				border: 0px solid rgba(85, 85, 85, 1);
			}

			.brz .brz-css-1tdvdmo .brz-menu__ul>.brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover {
				color: rgba(var(--brz-global-color2), 1);
				background-color: rgba(255, 255, 255, 0);
				border: 0px solid rgba(85, 85, 85, 1);
			}

			.brz .brz-css-1tdvdmo .brz-menu__ul>.brz-menu__item.brz-menu__item--current>.brz-a:not(.brz-a:active):hover {
				color: rgba(var(--brz-global-color2), 1);
				background-color: rgba(255, 255, 255, 0);
			}

			.brz .brz-css-1tdvdmo .brz-menu__ul>.brz-menu__item>.brz-a>.brz-icon-svg {
				margin: 0 15px 0 0;
			}

			.brz .brz-css-1tdvdmo .brz-menu__ul>.brz-menu__item>.brz-a:hover>.brz-icon-svg.brz-icon-svg-custom {
				background-color: rgba(var(--brz-global-color2), 1);
			}

			.brz .brz-css-1tdvdmo .brz-menu__ul>.brz-menu__item.brz-menu__item--current>.brz-a:not(.brz-a:active):hover>.brz-icon-svg.brz-icon-svg-custom {
				background-color: rgba(var(--brz-global-color2), 1);
			}

			.brz .brz-css-1tdvdmo .brz-menu__ul>.brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover>.brz-icon-svg.brz-icon-svg-custom {
				background-color: rgba(var(--brz-global-color2), 1);
			}

			.brz .brz-css-1tdvdmo .brz-menu__ul>.brz-menu__item {
				border-radius: 0px;
			}

			.brz .brz-css-1tdvdmo .brz-menu__ul>.brz-menu__item:hover {
				color: rgba(var(--brz-global-color2), 1);
				background-color: transparent;
				border: 0px solid rgba(85, 85, 85, 1);
			}

			.brz .brz-css-1tdvdmo .brz-menu__ul>.brz-menu__item>a {
				border-radius: 0px;
			}

			.brz .brz-css-1tdvdmo .brz-menu__sub-menu {
				font-family: var(--brz-heading5fontfamily, initial);
				border-radius: 0px;
			}

			.brz .brz-css-1tdvdmo .brz-menu__sub-menu:hover {
				color: rgba(255, 255, 255, 1);
				background-color: rgba(var(--brz-global-color3), 1);
				box-shadow: none;
			}

			.brz .brz-css-1tdvdmo .brz-menu__sub-menu .brz-menu__item>.brz-a {
				flex-flow: row nowrap;
			}

			.brz .brz-css-1tdvdmo:hover .brz-menu__sub-menu .brz-a:hover {
				color: rgba(255, 255, 255, 1);
			}

			.brz .brz-css-1tdvdmo .brz-menu__sub-menu .brz-a>.brz-icon-svg {
				margin: 0 15px 0 0;
				font-size: 12px;
			}

			.brz .brz-css-1tdvdmo:hover .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current>.brz-a {
				color: rgba(var(--brz-global-color8), 1);
			}

			.brz .brz-css-1tdvdmo:hover .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current {
				background-color: rgba(var(--brz-global-color3), 1);
			}

			.brz .brz-css-1tdvdmo:hover .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current:hover {
				background-color: rgba(var(--brz-global-color3), 1);
			}

			.brz .brz-css-1tdvdmo:hover .brz-menu__item--current .brz-menu__sub-menu {
				box-shadow: none;
			}

			.brz .brz-css-1tdvdmo .brz-menu__sub-menu>.brz-menu__item>.brz-a:hover>.brz-icon-svg.brz-icon-svg-custom {
				background-color: rgba(255, 255, 255, 1);
			}

			.brz .brz-css-1tdvdmo .brz-menu__sub-menu>.brz-menu__item:not(.brz-menu__item.brz-menu__item--current):hover>.brz-a>.brz-icon-svg.brz-icon-svg-custom {
				background-color: rgba(255, 255, 255, 1);
			}

			.brz .brz-css-1tdvdmo:hover .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current>.brz-a>.brz-icon-svg.brz-icon-svg-custom {
				background-color: rgba(var(--brz-global-color8), 1);
			}

			.brz .brz-css-1tdvdmo .brz-menu__item-dropdown .brz-menu__item:hover {
				background-color: rgba(var(--brz-global-color3), 1);
				color: rgba(255, 255, 255, 1);
			}

			.brz .brz-css-1tdvdmo .brz-menu__sub-menu .brz-menu__item-dropdown:hover .brz-a:hover:after {
				border-color: rgba(255, 255, 255, 1);
			}

			.brz .brz-css-1tdvdmo:hover .brz-menu__sub-menu>.brz-menu__item {
				border-bottom: 1px solid rgba(85, 85, 85, 1);
			}
		}

		@media (min-width:991px) {
			.brz .brz-css-1tdvdmo .brz-menu__ul {
				font-size: var(--brz-ugudlcdcxlbqfontsize, initial);
				font-weight: var(--brz-ugudlcdcxlbqfontweight, initial);
				font-weight: var(--brz-ugudlcdcxlbqbold, initial);
				line-height: var(--brz-ugudlcdcxlbqlineheight, initial);
				letter-spacing: var(--brz-ugudlcdcxlbqletterspacing, initial);
				font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation, initial);
				font-style: var(--brz-ugudlcdcxlbqitalic, initial);
				text-decoration: var(--brz-ugudlcdcxlbqtextdecoration, initial) !important;
				text-transform: var(--brz-ugudlcdcxlbqtexttransform, initial) !important;
			}

			.brz .brz-css-1tdvdmo:hover .brz-menu__ul {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1tdvdmo .brz-menu__ul>.brz-menu__item>.brz-a:hover {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1tdvdmo .brz-menu__ul>.brz-menu__item.brz-menu__item--opened>.brz-a:hover {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1tdvdmo .brz-menu__ul>.brz-menu__item.brz-menu__item--opened:hover {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1tdvdmo .brz-menu__ul>.brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1tdvdmo .brz-menu__ul>.brz-menu__item.brz-menu__item--current>.brz-a:not(.brz-a:active):hover {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1tdvdmo .brz-menu__ul>.brz-menu__item>.brz-a>.brz-icon-svg {
				font-size: 12px;
			}

			.brz .brz-css-1tdvdmo .brz-menu__ul>.brz-menu__item>.brz-a:hover>.brz-icon-svg.brz-icon-svg-custom {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1tdvdmo .brz-menu__ul>.brz-menu__item.brz-menu__item--current>.brz-a:not(.brz-a:active):hover>.brz-icon-svg.brz-icon-svg-custom {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1tdvdmo .brz-menu__ul>.brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover>.brz-icon-svg.brz-icon-svg-custom {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1tdvdmo .brz-menu__ul>.brz-menu__item {
				padding-top: 0px;
				padding-bottom: 0px;
				margin-right: 17.5px;
				margin-left: 17.5px;
			}

			.brz .brz-css-1tdvdmo .brz-menu__ul>.brz-menu__item:hover {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1tdvdmo .brz-menu__sub-menu {
				font-size: var(--brz-heading5fontsize, initial);
				font-weight: var(--brz-heading5fontweight, initial);
				font-weight: var(--brz-heading5bold, initial);
				line-height: var(--brz-heading5lineheight, initial);
				letter-spacing: var(--brz-heading5letterspacing, initial);
				font-variation-settings: var(--brz-heading5fontvariation, initial);
				font-style: var(--brz-heading5italic, initial);
				text-decoration: var(--brz-heading5textdecoration, initial) !important;
				text-transform: var(--brz-heading5texttransform, initial) !important;
				position: absolute;
				top: 0;
				width: 305px;
			}

			.brz .brz-css-1tdvdmo .brz-menu__sub-menu:hover {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1tdvdmo:hover .brz-menu__sub-menu .brz-a:hover {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1tdvdmo:hover .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current>.brz-a {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1tdvdmo:hover .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1tdvdmo:hover .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current:hover {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1tdvdmo:hover .brz-menu__item--current .brz-menu__sub-menu {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1tdvdmo .brz-menu__sub-menu>.brz-menu__item>.brz-a:hover>.brz-icon-svg.brz-icon-svg-custom {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1tdvdmo .brz-menu__sub-menu>.brz-menu__item:not(.brz-menu__item.brz-menu__item--current):hover>.brz-a>.brz-icon-svg.brz-icon-svg-custom {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1tdvdmo:hover .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current>.brz-a>.brz-icon-svg.brz-icon-svg-custom {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1tdvdmo .brz-menu__item-dropdown .brz-menu__item:hover {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1tdvdmo .brz-menu__sub-menu .brz-menu__item-dropdown:hover .brz-a:hover:after {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1tdvdmo [data-popper-placement='left-start'] {
				right: calc(100% + 5px);
			}

			.brz .brz-css-1tdvdmo [data-popper-placement='right-start'] {
				left: calc(100% + 5px);
			}

			.brz .brz-css-1tdvdmo>.brz-menu__ul>.brz-menu__item-dropdown>.brz-menu__sub-menu {
				top: calc(100% + 5px);
				width: 300px;
			}

			.brz .brz-css-1tdvdmo>.brz-menu__ul>.brz-menu__item-dropdown>[data-popper-placement='left-start'] {
				right: 0;
			}

			.brz .brz-css-1tdvdmo>.brz-menu__ul>.brz-menu__item-dropdown>[data-popper-placement='right-start'] {
				left: 0;
			}

			.brz .brz-css-1tdvdmo .brz-mega-menu__dropdown {
				display: none;
			}
		}

		@media (max-width:991px) and (min-width:768px) {
			.brz .brz-css-1tdvdmo .brz-menu__ul {
				font-family: var(--brz-buttonfontfamily, initial);
				margin: 0px -5px 0px -5px;
			}

			.brz .brz-css-1tdvdmo .brz-menu__sub-menu {
				font-family: var(--brz-buttonfontfamily, initial);
			}
		}

		@media (max-width:991px) and (min-width:768px) {
			.brz .brz-css-1tdvdmo .brz-menu__ul {
				font-size: var(--brz-buttontabletfontsize, initial);
				font-weight: var(--brz-buttontabletfontweight, initial);
				font-weight: var(--brz-buttontabletbold, initial);
				line-height: var(--brz-buttontabletlineheight, initial);
				letter-spacing: var(--brz-buttontabletletterspacing, initial);
				font-variation-settings: var(--brz-buttontabletfontvariation, initial);
				font-style: var(--brz-buttontabletitalic, initial);
				text-decoration: var(--brz-buttontablettextdecoration, initial) !important;
				text-transform: var(--brz-buttontablettexttransform, initial) !important;
			}

			.brz .brz-css-1tdvdmo .brz-menu__ul>.brz-menu__item {
				padding-top: 0px;
				padding-bottom: 0px;
				margin-right: 5px;
				margin-left: 5px;
			}

			.brz .brz-css-1tdvdmo .brz-menu__sub-menu {
				font-size: var(--brz-buttontabletfontsize, initial);
				font-weight: var(--brz-buttontabletfontweight, initial);
				font-weight: var(--brz-buttontabletbold, initial);
				line-height: var(--brz-buttontabletlineheight, initial);
				letter-spacing: var(--brz-buttontabletletterspacing, initial);
				font-variation-settings: var(--brz-buttontabletfontvariation, initial);
				font-style: var(--brz-buttontabletitalic, initial);
				text-decoration: var(--brz-buttontablettextdecoration, initial) !important;
				text-transform: var(--brz-buttontablettexttransform, initial) !important;
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-1tdvdmo .brz-menu__ul {
				font-family: var(--brz-buttonfontfamily, initial);
				margin: 0px -5px 0px -5px;
			}

			.brz .brz-css-1tdvdmo .brz-menu__sub-menu {
				font-family: var(--brz-buttonfontfamily, initial);
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-1tdvdmo .brz-menu__ul {
				font-size: var(--brz-buttonmobilefontsize, initial);
				font-weight: var(--brz-buttonmobilefontweight, initial);
				font-weight: var(--brz-buttonmobilebold, initial);
				line-height: var(--brz-buttonmobilelineheight, initial);
				letter-spacing: var(--brz-buttonmobileletterspacing, initial);
				font-variation-settings: var(--brz-buttonmobilefontvariation, initial);
				font-style: var(--brz-buttonmobileitalic, initial);
				text-decoration: var(--brz-buttonmobiletextdecoration, initial) !important;
				text-transform: var(--brz-buttonmobiletexttransform, initial) !important;
			}

			.brz .brz-css-1tdvdmo .brz-menu__ul>.brz-menu__item {
				padding-top: 0px;
				padding-bottom: 0px;
				margin-right: 5px;
				margin-left: 5px;
			}

			.brz .brz-css-1tdvdmo .brz-menu__sub-menu {
				font-size: var(--brz-buttonmobilefontsize, initial);
				font-weight: var(--brz-buttonmobilefontweight, initial);
				font-weight: var(--brz-buttonmobilebold, initial);
				line-height: var(--brz-buttonmobilelineheight, initial);
				letter-spacing: var(--brz-buttonmobileletterspacing, initial);
				font-variation-settings: var(--brz-buttonmobilefontvariation, initial);
				font-style: var(--brz-buttonmobileitalic, initial);
				text-decoration: var(--brz-buttonmobiletextdecoration, initial) !important;
				text-transform: var(--brz-buttonmobiletexttransform, initial) !important;
			}
		}

		.brz .brz-css-12dn42o .brz-mm-navbar .brz-mm-close {
			font-size: 16px;
			margin: 0;
			padding: 10px 15px 10px 10px;
		}

		.brz .brz-css-12dn42o .brz-mm-navbar .brz-mm-close {
			color: rgba(255, 255, 255, 1);
			background-color: #333;
		}

		.brz .brz-css-12dn42o .brz-menu__item {
			font-family: var(--brz-buttonfontfamily, initial);
		}

		.brz .brz-css-12dn42o .brz-menu__item {
			color: rgba(255, 255, 255, 1);
			border-color: rgba(85, 85, 85, 1);
		}

		.brz nav.brz-mm-menu.brz-css-12dn42o {
			background-color: rgba(51, 51, 51, .8);
		}

		.brz .brz-css-12dn42o.brz-mm-menu .brz-menu__item .brz-mm-listitem__text {
			padding: 10px 20px 10px 20px;
			flex-flow: row nowrap;
		}

		.brz .brz-css-12dn42o .brz-menu__item:hover>.brz-mm-listitem__text {
			color: rgba(255, 255, 255, 1);
		}

		.brz .brz-css-12dn42o .brz-menu__item .brz-mm-menu__item__icon.brz-icon-svg-custom {
			background-color: rgba(255, 255, 255, 1);
		}

		.brz .brz-css-12dn42o .brz-mm-navbar {
			color: rgba(255, 255, 255, 1);
		}

		.brz .brz-css-12dn42o .brz-menu__item.brz-mm-listitem_opened {
			color: rgba(255, 255, 255, 1);
		}

		.brz .brz-css-12dn42o.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels {
			background-image: none;
		}

		.brz .brz-css-12dn42o.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels,
		.brz .brz-css-12dn42o .brz-mm-panels>.brz-mm-panel {
			background-color: rgba(51, 51, 51, .8);
		}

		.brz .brz-css-12dn42o .brz-mm-panels>.brz-mm-panel {
			background-image: none;
			background-color: rgba(51, 51, 51, .8);
		}

		.brz .brz-css-12dn42o.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened>.brz-mm-listitem__text:after {
			border-color: rgba(85, 85, 85, 1);
		}

		.brz .brz-css-12dn42o .brz-mm-listitem {
			border-color: rgba(85, 85, 85, 1);
		}

		.brz .brz-css-12dn42o .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {
			color: rgba(255, 255, 255, 1);
		}

		.brz .brz-css-12dn42o .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active):hover>.brz-mm-listitem__text {
			color: rgba(255, 255, 255, 1);
		}

		.brz .brz-css-12dn42o .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active)>.brz-mm-listitem__text>.brz-icon-svg.brz-icon-svg-custom {
			background-color: rgba(255, 255, 255, 1);
		}

		@media (min-width:991px) {
			.brz .brz-css-12dn42o .brz-mm-navbar .brz-mm-close {
				transition-duration: .3s;
			}

			.brz .brz-css-12dn42o .brz-menu__item {
				font-size: var(--brz-buttonfontsize, initial);
				font-weight: var(--brz-buttonfontweight, initial);
				font-weight: var(--brz-buttonbold, initial);
				line-height: var(--brz-buttonlineheight, initial);
				letter-spacing: var(--brz-buttonletterspacing, initial);
				font-variation-settings: var(--brz-buttonfontvariation, initial);
				font-style: var(--brz-buttonitalic, initial);
				text-decoration: var(--brz-buttontextdecoration, initial) !important;
				text-transform: var(--brz-buttontexttransform, initial) !important;
			}

			.brz .brz-css-12dn42o .brz-menu__item {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz nav.brz-mm-menu.brz-css-12dn42o {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-12dn42o .brz-menu__item:hover>.brz-mm-listitem__text {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-12dn42o .brz-menu__item .brz-a {
				justify-content: flex-start;
				text-align: start;
			}

			.brz .brz-css-12dn42o .brz-mm-menu__item__icon {
				margin: 0 15px 0 0;
				font-size: 12px;
			}

			.brz .brz-css-12dn42o .brz-menu__item .brz-mm-menu__item__icon.brz-icon-svg-custom {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-12dn42o .brz-mm-navbar {
				font-family: var(--brz-buttonfontfamily, initial);
				font-size: var(--brz-buttonfontsize, initial);
				font-weight: var(--brz-buttonfontweight, initial);
				font-weight: var(--brz-buttonbold, initial);
				line-height: var(--brz-buttonlineheight, initial);
				letter-spacing: var(--brz-buttonletterspacing, initial);
				font-variation-settings: var(--brz-buttonfontvariation, initial);
				font-style: var(--brz-buttonitalic, initial);
				text-decoration: var(--brz-buttontextdecoration, initial) !important;
				text-transform: var(--brz-buttontexttransform, initial) !important;
				border-color: rgba(85, 85, 85, 1);
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-12dn42o .brz-menu__item.brz-mm-listitem_opened {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-12dn42o.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {
				height: calc(var(--brz-buttonlineheight, initial) * var(--brz-buttonfontsize, initial) + 10px + 10px);
				padding-right: 20px;
			}

			.brz .brz-css-12dn42o.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels,
			.brz .brz-css-12dn42o .brz-mm-panels>.brz-mm-panel {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-12dn42o.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened>.brz-mm-listitem__text:after {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-12dn42o .brz-mm-listitem {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}
		}

		@media (max-width:991px) and (min-width:768px) {
			.brz .brz-css-12dn42o .brz-mm-navbar .brz-mm-close {
				transition-duration: .3s;
			}

			.brz .brz-css-12dn42o .brz-menu__item {
				font-size: var(--brz-buttontabletfontsize, initial);
				font-weight: var(--brz-buttontabletfontweight, initial);
				font-weight: var(--brz-buttontabletbold, initial);
				line-height: var(--brz-buttontabletlineheight, initial);
				letter-spacing: var(--brz-buttontabletletterspacing, initial);
				font-variation-settings: var(--brz-buttontabletfontvariation, initial);
				font-style: var(--brz-buttontabletitalic, initial);
				text-decoration: var(--brz-buttontablettextdecoration, initial) !important;
				text-transform: var(--brz-buttontablettexttransform, initial) !important;
			}

			.brz .brz-css-12dn42o .brz-menu__item {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz nav.brz-mm-menu.brz-css-12dn42o {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-12dn42o .brz-menu__item:hover>.brz-mm-listitem__text {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-12dn42o .brz-menu__item .brz-a {
				justify-content: flex-start;
				text-align: start;
			}

			.brz .brz-css-12dn42o .brz-mm-menu__item__icon {
				margin: 0 15px 0 0;
				font-size: 12px;
			}

			.brz .brz-css-12dn42o .brz-menu__item .brz-mm-menu__item__icon.brz-icon-svg-custom {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-12dn42o .brz-mm-navbar {
				font-family: var(--brz-buttonfontfamily, initial);
				font-size: var(--brz-buttontabletfontsize, initial);
				font-weight: var(--brz-buttontabletfontweight, initial);
				font-weight: var(--brz-buttontabletbold, initial);
				line-height: var(--brz-buttontabletlineheight, initial);
				letter-spacing: var(--brz-buttontabletletterspacing, initial);
				font-variation-settings: var(--brz-buttontabletfontvariation, initial);
				font-style: var(--brz-buttontabletitalic, initial);
				text-decoration: var(--brz-buttontablettextdecoration, initial) !important;
				text-transform: var(--brz-buttontablettexttransform, initial) !important;
				border-color: rgba(85, 85, 85, 1);
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-12dn42o .brz-menu__item.brz-mm-listitem_opened {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-12dn42o.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {
				height: calc(var(--brz-buttontabletlineheight, initial) * var(--brz-buttontabletfontsize, initial) + 10px + 10px);
				padding-right: 20px;
			}

			.brz .brz-css-12dn42o.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels,
			.brz .brz-css-12dn42o .brz-mm-panels>.brz-mm-panel {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-12dn42o.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened>.brz-mm-listitem__text:after {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-12dn42o .brz-mm-listitem {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-12dn42o .brz-mm-navbar .brz-mm-close {
				transition-duration: .3s;
			}

			.brz .brz-css-12dn42o .brz-menu__item {
				font-size: var(--brz-buttonmobilefontsize, initial);
				font-weight: var(--brz-buttonmobilefontweight, initial);
				font-weight: var(--brz-buttonmobilebold, initial);
				line-height: var(--brz-buttonmobilelineheight, initial);
				letter-spacing: var(--brz-buttonmobileletterspacing, initial);
				font-variation-settings: var(--brz-buttonmobilefontvariation, initial);
				font-style: var(--brz-buttonmobileitalic, initial);
				text-decoration: var(--brz-buttonmobiletextdecoration, initial) !important;
				text-transform: var(--brz-buttonmobiletexttransform, initial) !important;
			}

			.brz .brz-css-12dn42o .brz-menu__item {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz nav.brz-mm-menu.brz-css-12dn42o {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-12dn42o .brz-menu__item:hover>.brz-mm-listitem__text {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-12dn42o .brz-menu__item .brz-a {
				justify-content: flex-start;
				text-align: start;
			}

			.brz .brz-css-12dn42o .brz-mm-menu__item__icon {
				margin: 0 15px 0 0;
				font-size: 12px;
			}

			.brz .brz-css-12dn42o .brz-menu__item .brz-mm-menu__item__icon.brz-icon-svg-custom {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-12dn42o .brz-mm-navbar {
				font-family: var(--brz-buttonfontfamily, initial);
				font-size: var(--brz-buttonmobilefontsize, initial);
				font-weight: var(--brz-buttonmobilefontweight, initial);
				font-weight: var(--brz-buttonmobilebold, initial);
				line-height: var(--brz-buttonmobilelineheight, initial);
				letter-spacing: var(--brz-buttonmobileletterspacing, initial);
				font-variation-settings: var(--brz-buttonmobilefontvariation, initial);
				font-style: var(--brz-buttonmobileitalic, initial);
				text-decoration: var(--brz-buttonmobiletextdecoration, initial) !important;
				text-transform: var(--brz-buttonmobiletexttransform, initial) !important;
				border-color: rgba(85, 85, 85, 1);
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-12dn42o .brz-menu__item.brz-mm-listitem_opened {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-12dn42o.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {
				height: calc(var(--brz-buttonmobilelineheight, initial) * var(--brz-buttonmobilefontsize, initial) + 10px + 10px);
				padding-right: 20px;
			}

			.brz .brz-css-12dn42o.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels,
			.brz .brz-css-12dn42o .brz-mm-panels>.brz-mm-panel {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-12dn42o.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened>.brz-mm-listitem__text:after {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-12dn42o .brz-mm-listitem {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}
		}

		.brz .brz-css-1x5bkh9 .brz-menu__item {
			font-family: var(--brz-buttonfontfamily, initial);
		}

		.brz .brz-css-1x5bkh9 .brz-menu__item {
			color: rgba(var(--brz-global-color7), 1);
			border-color: rgba(var(--brz-global-color6), 1);
		}

		.brz nav.brz-mm-menu.brz-css-1x5bkh9 {
			background-color: rgba(var(--brz-global-color3), 1);
		}

		.brz .brz-css-1x5bkh9.brz-mm-menu .brz-menu__item .brz-mm-listitem__text {
			padding: 10px 20px 10px 20px;
		}

		.brz .brz-css-1x5bkh9 .brz-menu__item:hover>.brz-mm-listitem__text {
			color: rgba(var(--brz-global-color7), 1);
		}

		.brz .brz-css-1x5bkh9 .brz-menu__item .brz-mm-menu__item__icon.brz-icon-svg-custom {
			background-color: rgba(var(--brz-global-color7), 1);
		}

		.brz .brz-css-1x5bkh9 .brz-mm-navbar {
			color: rgba(var(--brz-global-color7), 1);
		}

		.brz .brz-css-1x5bkh9 .brz-menu__item.brz-mm-listitem_opened {
			color: rgba(var(--brz-global-color7), 1);
		}

		.brz .brz-css-1x5bkh9.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels,
		.brz .brz-css-1x5bkh9 .brz-mm-panels>.brz-mm-panel {
			background-color: rgba(var(--brz-global-color3), 1);
		}

		.brz .brz-css-1x5bkh9 .brz-mm-panels>.brz-mm-panel {
			background-color: rgba(var(--brz-global-color3), 1);
		}

		.brz .brz-css-1x5bkh9.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened>.brz-mm-listitem__text:after {
			border-color: rgba(var(--brz-global-color6), 1);
		}

		.brz .brz-css-1x5bkh9 .brz-mm-listitem {
			border-color: rgba(var(--brz-global-color6), 1);
		}

		.brz .brz-css-1x5bkh9 .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {
			color: rgba(var(--brz-global-color2), 1);
		}

		.brz .brz-css-1x5bkh9 .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active):hover>.brz-mm-listitem__text {
			color: rgba(var(--brz-global-color2), 1);
		}

		.brz .brz-css-1x5bkh9 .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active)>.brz-mm-listitem__text>.brz-icon-svg.brz-icon-svg-custom {
			background-color: rgba(var(--brz-global-color2), 1);
		}

		@media (min-width:991px) {
			.brz .brz-css-1x5bkh9 .brz-menu__item {
				font-size: var(--brz-buttonfontsize, initial);
				font-weight: var(--brz-buttonfontweight, initial);
				font-weight: var(--brz-buttonbold, initial);
				line-height: var(--brz-buttonlineheight, initial);
				letter-spacing: var(--brz-buttonletterspacing, initial);
				font-variation-settings: var(--brz-buttonfontvariation, initial);
				font-style: var(--brz-buttonitalic, initial);
				text-decoration: var(--brz-buttontextdecoration, initial) !important;
				text-transform: var(--brz-buttontexttransform, initial) !important;
			}

			.brz .brz-css-1x5bkh9 .brz-menu__item .brz-a {
				justify-content: flex-start;
				text-align: start;
			}

			.brz .brz-css-1x5bkh9 .brz-mm-navbar {
				font-family: var(--brz-buttonfontfamily, initial);
				font-size: var(--brz-buttonfontsize, initial);
				font-weight: var(--brz-buttonfontweight, initial);
				font-weight: var(--brz-buttonbold, initial);
				line-height: var(--brz-buttonlineheight, initial);
				letter-spacing: var(--brz-buttonletterspacing, initial);
				font-variation-settings: var(--brz-buttonfontvariation, initial);
				font-style: var(--brz-buttonitalic, initial);
				text-decoration: var(--brz-buttontextdecoration, initial) !important;
				text-transform: var(--brz-buttontexttransform, initial) !important;
				border-color: rgba(var(--brz-global-color6), 1);
			}

			.brz .brz-css-1x5bkh9.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {
				height: calc(var(--brz-buttonlineheight, initial) * var(--brz-buttonfontsize, initial) + 10px + 10px);
				padding-right: 20px;
			}
		}

		@media (min-width:991px) {
			.brz .brz-css-1x5bkh9 .brz-mm-navbar .brz-mm-close {
				font-size: 16px;
				margin: 0;
				padding: 10px 15px 10px 10px;
			}

			.brz .brz-css-1x5bkh9 .brz-mm-navbar .brz-mm-close:hover {
				color: rgba(255, 255, 255, 1);
				background-color: #333;
			}

			.brz .brz-css-1x5bkh9 .brz-menu__item {
				font-family: var(--brz-buttonfontfamily, initial);
			}

			.brz .brz-css-1x5bkh9 .brz-menu__item:hover {
				color: rgba(var(--brz-global-color7), 1);
				border-color: rgba(var(--brz-global-color6), 1);
			}

			.brz nav.brz-mm-menu.brz-css-1x5bkh9 {
				background-color: rgba(var(--brz-global-color3), 1);
			}

			.brz .brz-css-1x5bkh9.brz-mm-menu .brz-menu__item .brz-mm-listitem__text {
				padding: 10px 20px 10px 20px;
				flex-flow: row nowrap;
			}

			.brz .brz-css-1x5bkh9:hover .brz-menu__item:hover>.brz-mm-listitem__text {
				color: rgba(var(--brz-global-color7), 1);
			}

			.brz .brz-css-1x5bkh9 .brz-menu__item:hover .brz-mm-menu__item__icon.brz-icon-svg-custom {
				background-color: rgba(var(--brz-global-color7), 1);
			}

			.brz .brz-css-1x5bkh9 .brz-mm-navbar {
				color: rgba(var(--brz-global-color7), 1);
			}

			.brz .brz-css-1x5bkh9:hover .brz-menu__item.brz-mm-listitem_opened {
				color: rgba(var(--brz-global-color7), 1);
			}

			.brz .brz-css-1x5bkh9.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels {
				background-image: none;
			}

			.brz .brz-css-1x5bkh9.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels,
			.brz .brz-css-1x5bkh9:hover .brz-mm-panels>.brz-mm-panel {
				background-color: rgba(var(--brz-global-color3), 1);
			}

			.brz .brz-css-1x5bkh9 .brz-mm-panels>.brz-mm-panel {
				background-image: none;
				background-color: rgba(var(--brz-global-color3), 1);
			}

			.brz .brz-css-1x5bkh9.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened>.brz-mm-listitem__text:after {
				border-color: rgba(var(--brz-global-color6), 1);
			}

			.brz .brz-css-1x5bkh9:hover .brz-mm-listitem {
				border-color: rgba(var(--brz-global-color6), 1);
			}

			.brz .brz-css-1x5bkh9 .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover {
				color: rgba(var(--brz-global-color2), 1);
			}

			.brz:hover .brz-css-1x5bkh9 .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active):hover>.brz-mm-listitem__text {
				color: rgba(var(--brz-global-color2), 1);
			}

			.brz .brz-css-1x5bkh9 .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active):hover>.brz-mm-listitem__text>.brz-icon-svg.brz-icon-svg-custom {
				background-color: rgba(var(--brz-global-color2), 1);
			}
		}

		@media (min-width:991px) {
			.brz .brz-css-1x5bkh9 .brz-mm-navbar .brz-mm-close:hover {
				transition-duration: .3s;
			}

			.brz .brz-css-1x5bkh9 .brz-menu__item {
				font-size: var(--brz-buttonfontsize, initial);
				font-weight: var(--brz-buttonfontweight, initial);
				font-weight: var(--brz-buttonbold, initial);
				line-height: var(--brz-buttonlineheight, initial);
				letter-spacing: var(--brz-buttonletterspacing, initial);
				font-variation-settings: var(--brz-buttonfontvariation, initial);
				font-style: var(--brz-buttonitalic, initial);
				text-decoration: var(--brz-buttontextdecoration, initial) !important;
				text-transform: var(--brz-buttontexttransform, initial) !important;
			}

			.brz .brz-css-1x5bkh9 .brz-menu__item:hover {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz nav.brz-mm-menu.brz-css-1x5bkh9 {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1x5bkh9:hover .brz-menu__item:hover>.brz-mm-listitem__text {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1x5bkh9 .brz-menu__item .brz-a {
				justify-content: flex-start;
				text-align: start;
			}

			.brz .brz-css-1x5bkh9 .brz-mm-menu__item__icon {
				margin: 0 15px 0 0;
				font-size: 12px;
			}

			.brz .brz-css-1x5bkh9 .brz-menu__item:hover .brz-mm-menu__item__icon.brz-icon-svg-custom {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1x5bkh9 .brz-mm-navbar {
				font-family: var(--brz-buttonfontfamily, initial);
				font-size: var(--brz-buttonfontsize, initial);
				font-weight: var(--brz-buttonfontweight, initial);
				font-weight: var(--brz-buttonbold, initial);
				line-height: var(--brz-buttonlineheight, initial);
				letter-spacing: var(--brz-buttonletterspacing, initial);
				font-variation-settings: var(--brz-buttonfontvariation, initial);
				font-style: var(--brz-buttonitalic, initial);
				text-decoration: var(--brz-buttontextdecoration, initial) !important;
				text-transform: var(--brz-buttontexttransform, initial) !important;
				border-color: rgba(var(--brz-global-color6), 1);
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1x5bkh9:hover .brz-menu__item.brz-mm-listitem_opened {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1x5bkh9.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {
				height: calc(var(--brz-buttonlineheight, initial) * var(--brz-buttonfontsize, initial) + 10px + 10px);
				padding-right: 20px;
			}

			.brz .brz-css-1x5bkh9.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels,
			.brz .brz-css-1x5bkh9:hover .brz-mm-panels>.brz-mm-panel {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1x5bkh9.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened>.brz-mm-listitem__text:after {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1x5bkh9:hover .brz-mm-listitem {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}
		}

		@media (max-width:991px) and (min-width:768px) {
			.brz .brz-css-1x5bkh9 .brz-menu__item {
				font-family: var(--brz-heading3fontfamily, initial);
			}

			.brz .brz-css-1x5bkh9.brz-mm-menu .brz-menu__item .brz-mm-listitem__text {
				padding: 15px 20px 15px 20px;
			}
		}

		@media (max-width:991px) and (min-width:768px) {
			.brz .brz-css-1x5bkh9 .brz-menu__item {
				font-size: var(--brz-heading3tabletfontsize, initial);
				font-weight: var(--brz-heading3tabletfontweight, initial);
				font-weight: var(--brz-heading3tabletbold, initial);
				line-height: var(--brz-heading3tabletlineheight, initial);
				letter-spacing: var(--brz-heading3tabletletterspacing, initial);
				font-variation-settings: var(--brz-heading3tabletfontvariation, initial);
				font-style: var(--brz-heading3tabletitalic, initial);
				text-decoration: var(--brz-heading3tablettextdecoration, initial) !important;
				text-transform: var(--brz-heading3tablettexttransform, initial) !important;
			}

			.brz .brz-css-1x5bkh9 .brz-menu__item .brz-a {
				justify-content: center;
				text-align: center;
			}

			.brz .brz-css-1x5bkh9 .brz-mm-navbar {
				font-family: var(--brz-heading3fontfamily, initial);
				font-size: var(--brz-heading3tabletfontsize, initial);
				font-weight: var(--brz-heading3tabletfontweight, initial);
				font-weight: var(--brz-heading3tabletbold, initial);
				line-height: var(--brz-heading3tabletlineheight, initial);
				letter-spacing: var(--brz-heading3tabletletterspacing, initial);
				font-variation-settings: var(--brz-heading3tabletfontvariation, initial);
				font-style: var(--brz-heading3tabletitalic, initial);
				text-decoration: var(--brz-heading3tablettextdecoration, initial) !important;
				text-transform: var(--brz-heading3tablettexttransform, initial) !important;
				border-color: rgba(var(--brz-global-color6), 1);
			}

			.brz .brz-css-1x5bkh9.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {
				height: calc(var(--brz-heading3tabletlineheight, initial) * var(--brz-heading3tabletfontsize, initial) + 15px + 15px);
				padding-right: 20px;
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-1x5bkh9 .brz-menu__item {
				font-family: var(--brz-heading3fontfamily, initial);
			}

			.brz .brz-css-1x5bkh9.brz-mm-menu .brz-menu__item .brz-mm-listitem__text {
				padding: 15px 20px 15px 20px;
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-1x5bkh9 .brz-menu__item {
				font-size: var(--brz-heading3mobilefontsize, initial);
				font-weight: var(--brz-heading3mobilefontweight, initial);
				font-weight: var(--brz-heading3mobilebold, initial);
				line-height: var(--brz-heading3mobilelineheight, initial);
				letter-spacing: var(--brz-heading3mobileletterspacing, initial);
				font-variation-settings: var(--brz-heading3mobilefontvariation, initial);
				font-style: var(--brz-heading3mobileitalic, initial);
				text-decoration: var(--brz-heading3mobiletextdecoration, initial) !important;
				text-transform: var(--brz-heading3mobiletexttransform, initial) !important;
			}

			.brz .brz-css-1x5bkh9 .brz-menu__item .brz-a {
				justify-content: center;
				text-align: center;
			}

			.brz .brz-css-1x5bkh9 .brz-mm-navbar {
				font-family: var(--brz-heading3fontfamily, initial);
				font-size: var(--brz-heading3mobilefontsize, initial);
				font-weight: var(--brz-heading3mobilefontweight, initial);
				font-weight: var(--brz-heading3mobilebold, initial);
				line-height: var(--brz-heading3mobilelineheight, initial);
				letter-spacing: var(--brz-heading3mobileletterspacing, initial);
				font-variation-settings: var(--brz-heading3mobilefontvariation, initial);
				font-style: var(--brz-heading3mobileitalic, initial);
				text-decoration: var(--brz-heading3mobiletextdecoration, initial) !important;
				text-transform: var(--brz-heading3mobiletexttransform, initial) !important;
				border-color: rgba(var(--brz-global-color6), 1);
			}

			.brz .brz-css-1x5bkh9.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {
				height: calc(var(--brz-heading3mobilelineheight, initial) * var(--brz-heading3mobilefontsize, initial) + 15px + 15px);
				padding-right: 20px;
			}
		}

		.brz .brz-css-19ioiov {
			padding: 8px;
			margin: 8px 0px 8px 0px;
			justify-content: flex-end;
		}

		@media (min-width:991px) {
			.brz .brz-css-19ioiov {
				display: none;
			}
		}

		@media (min-width:991px) {
			.brz .brz-css-19ioiov {
				padding: 8px;
				margin: 8px 0px 8px 0px;
				justify-content: flex-end;
				position: relative;
			}

			.brz .brz-css-19ioiov .brz-wrapper-transform {
				transform: none;
			}
		}

		@media (min-width:991px) {
			.brz .brz-css-19ioiov {
				display: flex;
				display: none;
				z-index: auto;
				position: relative;
			}
		}

		@media (max-width:991px) and (min-width:768px) {
			.brz .brz-css-19ioiov {
				padding: 0;
				margin: 5px 0px 5px 0px;
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-19ioiov {
				padding: 0;
				margin: 10px 0px 10px 0px;
			}
		}

		@media (min-width:991px) {
			.brz .brz-css-1mxc0dk .brz-mm-menu__icon {
				font-size: 18px;
			}

			.brz .brz-css-1mxc0dk .brz-mm-menu__icon {
				color: rgba(51, 51, 51, 1);
			}
		}

		@media (min-width:991px) {
			.brz .brz-css-1mxc0dk .brz-mm-menu__icon {
				display: none;
				font-size: 18px;
			}

			.brz .brz-css-1mxc0dk:hover .brz-mm-menu__icon {
				color: rgba(51, 51, 51, 1);
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1mxc0dk .brz-menu {
				display: flex;
			}
		}

		@media (max-width:991px) and (min-width:768px) {
			.brz .brz-css-1mxc0dk .brz-mm-menu__icon {
				font-size: 28px;
			}

			.brz .brz-css-1mxc0dk .brz-mm-menu__icon {
				color: rgba(var(--brz-global-color7), 1);
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-1mxc0dk .brz-mm-menu__icon {
				font-size: 28px;
			}

			.brz .brz-css-1mxc0dk .brz-mm-menu__icon {
				color: rgba(var(--brz-global-color7), 1);
			}
		}

		.brz .brz-css-1dgf3el .brz-menu__ul {
			font-family: var(--brz-heading4fontfamily, initial);
			margin: 0px -17.5px 0px -17.5px;
		}

		.brz .brz-css-1dgf3el .brz-menu__ul {
			color: rgba(var(--brz-global-color7), 1);
		}

		.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item>.brz-a {
			color: rgba(var(--brz-global-color7), 1);
		}

		.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item.brz-menu__item--opened>.brz-a {
			color: rgba(var(--brz-global-color7), 1);
		}

		.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item.brz-menu__item--opened {
			color: rgba(var(--brz-global-color7), 1);
		}

		.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {
			color: rgba(var(--brz-global-color2), 1);
		}

		.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item.brz-menu__item--current>.brz-a:not(.brz-a:active) {
			color: rgba(var(--brz-global-color2), 1);
		}

		.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item>.brz-a>.brz-icon-svg {
			margin: 0 4px 0 0;
		}

		.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item>.brz-a>.brz-icon-svg.brz-icon-svg-custom {
			background-color: rgba(var(--brz-global-color7), 1);
		}

		.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item.brz-menu__item--current>.brz-a:not(.brz-a:active)>.brz-icon-svg.brz-icon-svg-custom {
			background-color: rgba(var(--brz-global-color2), 1);
		}

		.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active)>.brz-icon-svg.brz-icon-svg-custom {
			background-color: rgba(var(--brz-global-color2), 1);
		}

		.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item {
			color: rgba(var(--brz-global-color7), 1);
		}

		.brz .brz-css-1dgf3el .brz-menu__sub-menu {
			font-family: var(--brz-heading5fontfamily, initial);
		}

		.brz .brz-css-1dgf3el .brz-menu__sub-menu {
			background-color: rgba(var(--brz-global-color3), 1);
		}

		.brz .brz-css-1dgf3el .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current>.brz-a {
			color: rgba(var(--brz-global-color2), 1);
		}

		.brz .brz-css-1dgf3el .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current {
			background-color: rgba(var(--brz-global-color3), 1);
		}

		.brz .brz-css-1dgf3el .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current:hover {
			background-color: rgba(var(--brz-global-color3), 1);
		}

		.brz .brz-css-1dgf3el .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current>.brz-a>.brz-icon-svg.brz-icon-svg-custom {
			background-color: rgba(var(--brz-global-color2), 1);
		}

		.brz .brz-css-1dgf3el .brz-menu__item-dropdown .brz-menu__item {
			background-color: rgba(var(--brz-global-color3), 1);
		}

		@media (min-width:991px) {
			.brz .brz-css-1dgf3el .brz-menu__ul {
				font-size: var(--brz-heading4fontsize, initial);
				font-weight: var(--brz-heading4fontweight, initial);
				font-weight: var(--brz-heading4bold, initial);
				line-height: var(--brz-heading4lineheight, initial);
				letter-spacing: var(--brz-heading4letterspacing, initial);
				font-variation-settings: var(--brz-heading4fontvariation, initial);
				font-style: var(--brz-heading4italic, initial);
				text-decoration: var(--brz-heading4textdecoration, initial) !important;
				text-transform: var(--brz-heading4texttransform, initial) !important;
			}

			.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item>.brz-a>.brz-icon-svg {
				font-size: 20px;
			}

			.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item {
				padding-top: 0px;
				padding-bottom: 0px;
				margin-right: 17.5px;
				margin-left: 17.5px;
			}

			.brz .brz-css-1dgf3el .brz-menu__sub-menu {
				font-size: var(--brz-heading5fontsize, initial);
				font-weight: var(--brz-heading5fontweight, initial);
				font-weight: var(--brz-heading5bold, initial);
				line-height: var(--brz-heading5lineheight, initial);
				letter-spacing: var(--brz-heading5letterspacing, initial);
				font-variation-settings: var(--brz-heading5fontvariation, initial);
				font-style: var(--brz-heading5italic, initial);
				text-decoration: var(--brz-heading5textdecoration, initial) !important;
				text-transform: var(--brz-heading5texttransform, initial) !important;
			}
		}

		@media (min-width:991px) {
			.brz .brz-css-1dgf3el .brz-menu__ul {
				font-family: var(--brz-heading4fontfamily, initial);
				display: flex;
				flex-wrap: wrap;
				justify-content: inherit;
				align-items: center;
				max-width: none;
				margin: 0px -17.5px 0px -17.5px;
			}

			.brz .brz-css-1dgf3el:hover .brz-menu__ul {
				color: rgba(var(--brz-global-color2), 1);
			}

			.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item>.brz-a {
				flex-flow: row nowrap;
				padding: 0px 5px 0px 5px;
			}

			.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item>.brz-a:hover {
				color: rgba(var(--brz-global-color2), 1);
				background-color: rgba(255, 255, 255, 0);
			}

			.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item.brz-menu__item--opened>.brz-a:hover {
				color: rgba(var(--brz-global-color2), 1);
				background-color: rgba(255, 255, 255, 0);
			}

			.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item.brz-menu__item--opened:hover {
				color: rgba(var(--brz-global-color2), 1);
				background-color: transparent;
				border: 0px solid rgba(85, 85, 85, 1);
			}

			.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover {
				color: rgba(var(--brz-global-color2), 1);
				background-color: rgba(255, 255, 255, 0);
				border: 0px solid rgba(85, 85, 85, 1);
			}

			.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item.brz-menu__item--current>.brz-a:not(.brz-a:active):hover {
				color: rgba(var(--brz-global-color2), 1);
				background-color: rgba(255, 255, 255, 0);
			}

			.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item>.brz-a>.brz-icon-svg {
				margin: 0 4px 0 0;
			}

			.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item>.brz-a:hover>.brz-icon-svg.brz-icon-svg-custom {
				background-color: rgba(var(--brz-global-color2), 1);
			}

			.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item.brz-menu__item--current>.brz-a:not(.brz-a:active):hover>.brz-icon-svg.brz-icon-svg-custom {
				background-color: rgba(var(--brz-global-color2), 1);
			}

			.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover>.brz-icon-svg.brz-icon-svg-custom {
				background-color: rgba(var(--brz-global-color2), 1);
			}

			.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item {
				border-radius: 0px;
			}

			.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item:hover {
				color: rgba(var(--brz-global-color2), 1);
				background-color: transparent;
				border: 0px solid rgba(85, 85, 85, 1);
			}

			.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item>a {
				border-radius: 0px;
			}

			.brz .brz-css-1dgf3el .brz-menu__sub-menu {
				font-family: var(--brz-heading5fontfamily, initial);
				border-radius: 0px;
			}

			.brz .brz-css-1dgf3el .brz-menu__sub-menu:hover {
				color: rgba(255, 255, 255, 1);
				background-color: rgba(var(--brz-global-color3), 1);
				box-shadow: none;
			}

			.brz .brz-css-1dgf3el .brz-menu__sub-menu .brz-menu__item>.brz-a {
				flex-flow: row nowrap;
			}

			.brz .brz-css-1dgf3el:hover .brz-menu__sub-menu .brz-a:hover {
				color: rgba(255, 255, 255, 1);
			}

			.brz .brz-css-1dgf3el .brz-menu__sub-menu .brz-a>.brz-icon-svg {
				margin: 0 15px 0 0;
				font-size: 12px;
			}

			.brz .brz-css-1dgf3el:hover .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current>.brz-a {
				color: rgba(var(--brz-global-color2), 1);
			}

			.brz .brz-css-1dgf3el:hover .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current {
				background-color: rgba(var(--brz-global-color3), 1);
			}

			.brz .brz-css-1dgf3el:hover .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current:hover {
				background-color: rgba(var(--brz-global-color3), 1);
			}

			.brz .brz-css-1dgf3el:hover .brz-menu__item--current .brz-menu__sub-menu {
				box-shadow: none;
			}

			.brz .brz-css-1dgf3el .brz-menu__sub-menu>.brz-menu__item>.brz-a:hover>.brz-icon-svg.brz-icon-svg-custom {
				background-color: rgba(255, 255, 255, 1);
			}

			.brz .brz-css-1dgf3el .brz-menu__sub-menu>.brz-menu__item:not(.brz-menu__item.brz-menu__item--current):hover>.brz-a>.brz-icon-svg.brz-icon-svg-custom {
				background-color: rgba(255, 255, 255, 1);
			}

			.brz .brz-css-1dgf3el:hover .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current>.brz-a>.brz-icon-svg.brz-icon-svg-custom {
				background-color: rgba(var(--brz-global-color2), 1);
			}

			.brz .brz-css-1dgf3el .brz-menu__item-dropdown .brz-menu__item:hover {
				background-color: rgba(var(--brz-global-color3), 1);
				color: rgba(255, 255, 255, 1);
			}

			.brz .brz-css-1dgf3el .brz-menu__sub-menu .brz-menu__item-dropdown:hover .brz-a:hover:after {
				border-color: rgba(255, 255, 255, 1);
			}

			.brz .brz-css-1dgf3el:hover .brz-menu__sub-menu>.brz-menu__item {
				border-bottom: 1px solid rgba(85, 85, 85, 1);
			}
		}

		@media (min-width:991px) {
			.brz .brz-css-1dgf3el .brz-menu__ul {
				font-size: var(--brz-heading4fontsize, initial);
				font-weight: var(--brz-heading4fontweight, initial);
				font-weight: var(--brz-heading4bold, initial);
				line-height: var(--brz-heading4lineheight, initial);
				letter-spacing: var(--brz-heading4letterspacing, initial);
				font-variation-settings: var(--brz-heading4fontvariation, initial);
				font-style: var(--brz-heading4italic, initial);
				text-decoration: var(--brz-heading4textdecoration, initial) !important;
				text-transform: var(--brz-heading4texttransform, initial) !important;
			}

			.brz .brz-css-1dgf3el:hover .brz-menu__ul {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item>.brz-a:hover {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item.brz-menu__item--opened>.brz-a:hover {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item.brz-menu__item--opened:hover {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item.brz-menu__item--current>.brz-a:not(.brz-a:active):hover {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item>.brz-a>.brz-icon-svg {
				font-size: 20px;
			}

			.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item>.brz-a:hover>.brz-icon-svg.brz-icon-svg-custom {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item.brz-menu__item--current>.brz-a:not(.brz-a:active):hover>.brz-icon-svg.brz-icon-svg-custom {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover>.brz-icon-svg.brz-icon-svg-custom {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item {
				padding-top: 0px;
				padding-bottom: 0px;
				margin-right: 17.5px;
				margin-left: 17.5px;
			}

			.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item:hover {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1dgf3el .brz-menu__sub-menu {
				font-size: var(--brz-heading5fontsize, initial);
				font-weight: var(--brz-heading5fontweight, initial);
				font-weight: var(--brz-heading5bold, initial);
				line-height: var(--brz-heading5lineheight, initial);
				letter-spacing: var(--brz-heading5letterspacing, initial);
				font-variation-settings: var(--brz-heading5fontvariation, initial);
				font-style: var(--brz-heading5italic, initial);
				text-decoration: var(--brz-heading5textdecoration, initial) !important;
				text-transform: var(--brz-heading5texttransform, initial) !important;
				position: absolute;
				top: 0;
				width: 305px;
			}

			.brz .brz-css-1dgf3el .brz-menu__sub-menu:hover {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1dgf3el:hover .brz-menu__sub-menu .brz-a:hover {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1dgf3el:hover .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current>.brz-a {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1dgf3el:hover .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1dgf3el:hover .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current:hover {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1dgf3el:hover .brz-menu__item--current .brz-menu__sub-menu {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1dgf3el .brz-menu__sub-menu>.brz-menu__item>.brz-a:hover>.brz-icon-svg.brz-icon-svg-custom {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1dgf3el .brz-menu__sub-menu>.brz-menu__item:not(.brz-menu__item.brz-menu__item--current):hover>.brz-a>.brz-icon-svg.brz-icon-svg-custom {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1dgf3el:hover .brz-menu__sub-menu>.brz-menu__item.brz-menu__item--current>.brz-a>.brz-icon-svg.brz-icon-svg-custom {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1dgf3el .brz-menu__item-dropdown .brz-menu__item:hover {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1dgf3el .brz-menu__sub-menu .brz-menu__item-dropdown:hover .brz-a:hover:after {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1dgf3el [data-popper-placement='left-start'] {
				right: calc(100% + 5px);
			}

			.brz .brz-css-1dgf3el [data-popper-placement='right-start'] {
				left: calc(100% + 5px);
			}

			.brz .brz-css-1dgf3el>.brz-menu__ul>.brz-menu__item-dropdown>.brz-menu__sub-menu {
				top: calc(100% + 5px);
				width: 300px;
			}

			.brz .brz-css-1dgf3el>.brz-menu__ul>.brz-menu__item-dropdown>[data-popper-placement='left-start'] {
				right: 0;
			}

			.brz .brz-css-1dgf3el>.brz-menu__ul>.brz-menu__item-dropdown>[data-popper-placement='right-start'] {
				left: 0;
			}

			.brz .brz-css-1dgf3el .brz-mega-menu__dropdown {
				display: none;
			}
		}

		@media (max-width:991px) and (min-width:768px) {
			.brz .brz-css-1dgf3el .brz-menu__ul {
				font-family: var(--brz-buttonfontfamily, initial);
				margin: 0px -5px 0px -5px;
			}

			.brz .brz-css-1dgf3el .brz-menu__sub-menu {
				font-family: var(--brz-buttonfontfamily, initial);
			}
		}

		@media (max-width:991px) and (min-width:768px) {
			.brz .brz-css-1dgf3el .brz-menu__ul {
				font-size: var(--brz-buttontabletfontsize, initial);
				font-weight: var(--brz-buttontabletfontweight, initial);
				font-weight: var(--brz-buttontabletbold, initial);
				line-height: var(--brz-buttontabletlineheight, initial);
				letter-spacing: var(--brz-buttontabletletterspacing, initial);
				font-variation-settings: var(--brz-buttontabletfontvariation, initial);
				font-style: var(--brz-buttontabletitalic, initial);
				text-decoration: var(--brz-buttontablettextdecoration, initial) !important;
				text-transform: var(--brz-buttontablettexttransform, initial) !important;
			}

			.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item>.brz-a>.brz-icon-svg {
				font-size: 12px;
			}

			.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item {
				padding-top: 0px;
				padding-bottom: 0px;
				margin-right: 5px;
				margin-left: 5px;
			}

			.brz .brz-css-1dgf3el .brz-menu__sub-menu {
				font-size: var(--brz-buttontabletfontsize, initial);
				font-weight: var(--brz-buttontabletfontweight, initial);
				font-weight: var(--brz-buttontabletbold, initial);
				line-height: var(--brz-buttontabletlineheight, initial);
				letter-spacing: var(--brz-buttontabletletterspacing, initial);
				font-variation-settings: var(--brz-buttontabletfontvariation, initial);
				font-style: var(--brz-buttontabletitalic, initial);
				text-decoration: var(--brz-buttontablettextdecoration, initial) !important;
				text-transform: var(--brz-buttontablettexttransform, initial) !important;
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-1dgf3el .brz-menu__ul {
				font-family: var(--brz-buttonfontfamily, initial);
				margin: 0px -5px 0px -5px;
			}

			.brz .brz-css-1dgf3el .brz-menu__sub-menu {
				font-family: var(--brz-buttonfontfamily, initial);
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-1dgf3el .brz-menu__ul {
				font-size: var(--brz-buttonmobilefontsize, initial);
				font-weight: var(--brz-buttonmobilefontweight, initial);
				font-weight: var(--brz-buttonmobilebold, initial);
				line-height: var(--brz-buttonmobilelineheight, initial);
				letter-spacing: var(--brz-buttonmobileletterspacing, initial);
				font-variation-settings: var(--brz-buttonmobilefontvariation, initial);
				font-style: var(--brz-buttonmobileitalic, initial);
				text-decoration: var(--brz-buttonmobiletextdecoration, initial) !important;
				text-transform: var(--brz-buttonmobiletexttransform, initial) !important;
			}

			.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item>.brz-a>.brz-icon-svg {
				font-size: 12px;
			}

			.brz .brz-css-1dgf3el .brz-menu__ul>.brz-menu__item {
				padding-top: 0px;
				padding-bottom: 0px;
				margin-right: 5px;
				margin-left: 5px;
			}

			.brz .brz-css-1dgf3el .brz-menu__sub-menu {
				font-size: var(--brz-buttonmobilefontsize, initial);
				font-weight: var(--brz-buttonmobilefontweight, initial);
				font-weight: var(--brz-buttonmobilebold, initial);
				line-height: var(--brz-buttonmobilelineheight, initial);
				letter-spacing: var(--brz-buttonmobileletterspacing, initial);
				font-variation-settings: var(--brz-buttonmobilefontvariation, initial);
				font-style: var(--brz-buttonmobileitalic, initial);
				text-decoration: var(--brz-buttonmobiletextdecoration, initial) !important;
				text-transform: var(--brz-buttonmobiletexttransform, initial) !important;
			}
		}

		.brz .brz-css-1kwae1d .brz-menu__item {
			font-family: var(--brz-buttonfontfamily, initial);
		}

		.brz .brz-css-1kwae1d .brz-menu__item {
			color: rgba(var(--brz-global-color7), 1);
			border-color: rgba(var(--brz-global-color6), 1);
		}

		.brz nav.brz-mm-menu.brz-css-1kwae1d {
			background-color: rgba(var(--brz-global-color3), 1);
		}

		.brz .brz-css-1kwae1d.brz-mm-menu .brz-menu__item .brz-mm-listitem__text {
			padding: 10px 20px 10px 20px;
		}

		.brz .brz-css-1kwae1d .brz-menu__item:hover>.brz-mm-listitem__text {
			color: rgba(var(--brz-global-color7), 1);
		}

		.brz .brz-css-1kwae1d .brz-menu__item .brz-mm-menu__item__icon.brz-icon-svg-custom {
			background-color: rgba(var(--brz-global-color7), 1);
		}

		.brz .brz-css-1kwae1d .brz-mm-navbar {
			color: rgba(var(--brz-global-color7), 1);
		}

		.brz .brz-css-1kwae1d .brz-menu__item.brz-mm-listitem_opened {
			color: rgba(var(--brz-global-color7), 1);
		}

		.brz .brz-css-1kwae1d.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels,
		.brz .brz-css-1kwae1d .brz-mm-panels>.brz-mm-panel {
			background-color: rgba(var(--brz-global-color3), 1);
		}

		.brz .brz-css-1kwae1d .brz-mm-panels>.brz-mm-panel {
			background-color: rgba(var(--brz-global-color3), 1);
		}

		.brz .brz-css-1kwae1d.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened>.brz-mm-listitem__text:after {
			border-color: rgba(var(--brz-global-color6), 1);
		}

		.brz .brz-css-1kwae1d .brz-mm-listitem {
			border-color: rgba(var(--brz-global-color6), 1);
		}

		.brz .brz-css-1kwae1d .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {
			color: rgba(var(--brz-global-color2), 1);
		}

		.brz .brz-css-1kwae1d .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active):hover>.brz-mm-listitem__text {
			color: rgba(var(--brz-global-color2), 1);
		}

		.brz .brz-css-1kwae1d .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active)>.brz-mm-listitem__text>.brz-icon-svg.brz-icon-svg-custom {
			background-color: rgba(var(--brz-global-color2), 1);
		}

		@media (min-width:991px) {
			.brz .brz-css-1kwae1d .brz-menu__item {
				font-size: var(--brz-buttonfontsize, initial);
				font-weight: var(--brz-buttonfontweight, initial);
				font-weight: var(--brz-buttonbold, initial);
				line-height: var(--brz-buttonlineheight, initial);
				letter-spacing: var(--brz-buttonletterspacing, initial);
				font-variation-settings: var(--brz-buttonfontvariation, initial);
				font-style: var(--brz-buttonitalic, initial);
				text-decoration: var(--brz-buttontextdecoration, initial) !important;
				text-transform: var(--brz-buttontexttransform, initial) !important;
			}

			.brz .brz-css-1kwae1d .brz-menu__item .brz-a {
				justify-content: flex-start;
				text-align: start;
			}

			.brz .brz-css-1kwae1d .brz-mm-menu__item__icon {
				margin: 0 15px 0 0;
				font-size: 12px;
			}

			.brz .brz-css-1kwae1d .brz-mm-navbar {
				font-family: var(--brz-buttonfontfamily, initial);
				font-size: var(--brz-buttonfontsize, initial);
				font-weight: var(--brz-buttonfontweight, initial);
				font-weight: var(--brz-buttonbold, initial);
				line-height: var(--brz-buttonlineheight, initial);
				letter-spacing: var(--brz-buttonletterspacing, initial);
				font-variation-settings: var(--brz-buttonfontvariation, initial);
				font-style: var(--brz-buttonitalic, initial);
				text-decoration: var(--brz-buttontextdecoration, initial) !important;
				text-transform: var(--brz-buttontexttransform, initial) !important;
				border-color: rgba(var(--brz-global-color6), 1);
			}

			.brz .brz-css-1kwae1d.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {
				height: calc(var(--brz-buttonlineheight, initial) * var(--brz-buttonfontsize, initial) + 10px + 10px);
				padding-right: 20px;
			}
		}

		@media (min-width:991px) {
			.brz .brz-css-1kwae1d .brz-mm-navbar .brz-mm-close {
				font-size: 16px;
				margin: 0;
				padding: 10px 15px 10px 10px;
			}

			.brz .brz-css-1kwae1d .brz-mm-navbar .brz-mm-close:hover {
				color: rgba(255, 255, 255, 1);
				background-color: #333;
			}

			.brz .brz-css-1kwae1d .brz-menu__item {
				font-family: var(--brz-buttonfontfamily, initial);
			}

			.brz .brz-css-1kwae1d .brz-menu__item:hover {
				color: rgba(var(--brz-global-color7), 1);
				border-color: rgba(var(--brz-global-color6), 1);
			}

			.brz nav.brz-mm-menu.brz-css-1kwae1d {
				background-color: rgba(var(--brz-global-color3), 1);
			}

			.brz .brz-css-1kwae1d.brz-mm-menu .brz-menu__item .brz-mm-listitem__text {
				padding: 10px 20px 10px 20px;
				flex-flow: row nowrap;
			}

			.brz .brz-css-1kwae1d:hover .brz-menu__item:hover>.brz-mm-listitem__text {
				color: rgba(var(--brz-global-color7), 1);
			}

			.brz .brz-css-1kwae1d .brz-menu__item:hover .brz-mm-menu__item__icon.brz-icon-svg-custom {
				background-color: rgba(var(--brz-global-color7), 1);
			}

			.brz .brz-css-1kwae1d .brz-mm-navbar {
				color: rgba(var(--brz-global-color7), 1);
			}

			.brz .brz-css-1kwae1d:hover .brz-menu__item.brz-mm-listitem_opened {
				color: rgba(var(--brz-global-color7), 1);
			}

			.brz .brz-css-1kwae1d.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels {
				background-image: none;
			}

			.brz .brz-css-1kwae1d.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels,
			.brz .brz-css-1kwae1d:hover .brz-mm-panels>.brz-mm-panel {
				background-color: rgba(var(--brz-global-color3), 1);
			}

			.brz .brz-css-1kwae1d .brz-mm-panels>.brz-mm-panel {
				background-image: none;
				background-color: rgba(var(--brz-global-color3), 1);
			}

			.brz .brz-css-1kwae1d.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened>.brz-mm-listitem__text:after {
				border-color: rgba(var(--brz-global-color6), 1);
			}

			.brz .brz-css-1kwae1d:hover .brz-mm-listitem {
				border-color: rgba(var(--brz-global-color6), 1);
			}

			.brz .brz-css-1kwae1d .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover {
				color: rgba(var(--brz-global-color2), 1);
			}

			.brz:hover .brz-css-1kwae1d .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active):hover>.brz-mm-listitem__text {
				color: rgba(var(--brz-global-color2), 1);
			}

			.brz .brz-css-1kwae1d .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active):hover>.brz-mm-listitem__text>.brz-icon-svg.brz-icon-svg-custom {
				background-color: rgba(var(--brz-global-color2), 1);
			}
		}

		@media (min-width:991px) {
			.brz .brz-css-1kwae1d .brz-mm-navbar .brz-mm-close:hover {
				transition-duration: .3s;
			}

			.brz .brz-css-1kwae1d .brz-menu__item {
				font-size: var(--brz-buttonfontsize, initial);
				font-weight: var(--brz-buttonfontweight, initial);
				font-weight: var(--brz-buttonbold, initial);
				line-height: var(--brz-buttonlineheight, initial);
				letter-spacing: var(--brz-buttonletterspacing, initial);
				font-variation-settings: var(--brz-buttonfontvariation, initial);
				font-style: var(--brz-buttonitalic, initial);
				text-decoration: var(--brz-buttontextdecoration, initial) !important;
				text-transform: var(--brz-buttontexttransform, initial) !important;
			}

			.brz .brz-css-1kwae1d .brz-menu__item:hover {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz nav.brz-mm-menu.brz-css-1kwae1d {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1kwae1d:hover .brz-menu__item:hover>.brz-mm-listitem__text {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1kwae1d .brz-menu__item .brz-a {
				justify-content: flex-start;
				text-align: start;
			}

			.brz .brz-css-1kwae1d .brz-mm-menu__item__icon {
				margin: 0 15px 0 0;
				font-size: 12px;
			}

			.brz .brz-css-1kwae1d .brz-menu__item:hover .brz-mm-menu__item__icon.brz-icon-svg-custom {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1kwae1d .brz-mm-navbar {
				font-family: var(--brz-buttonfontfamily, initial);
				font-size: var(--brz-buttonfontsize, initial);
				font-weight: var(--brz-buttonfontweight, initial);
				font-weight: var(--brz-buttonbold, initial);
				line-height: var(--brz-buttonlineheight, initial);
				letter-spacing: var(--brz-buttonletterspacing, initial);
				font-variation-settings: var(--brz-buttonfontvariation, initial);
				font-style: var(--brz-buttonitalic, initial);
				text-decoration: var(--brz-buttontextdecoration, initial) !important;
				text-transform: var(--brz-buttontexttransform, initial) !important;
				border-color: rgba(var(--brz-global-color6), 1);
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1kwae1d:hover .brz-menu__item.brz-mm-listitem_opened {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1kwae1d.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {
				height: calc(var(--brz-buttonlineheight, initial) * var(--brz-buttonfontsize, initial) + 10px + 10px);
				padding-right: 20px;
			}

			.brz .brz-css-1kwae1d.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels,
			.brz .brz-css-1kwae1d:hover .brz-mm-panels>.brz-mm-panel {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1kwae1d.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened>.brz-mm-listitem__text:after {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}

			.brz .brz-css-1kwae1d:hover .brz-mm-listitem {
				transition-duration: .5s;
				transition-property: filter, color, background, border-color, box-shadow;
			}
		}

		@media (max-width:991px) and (min-width:768px) {
			.brz .brz-css-1kwae1d .brz-menu__item {
				font-family: var(--brz-heading5fontfamily, initial);
			}

			.brz .brz-css-1kwae1d .brz-menu__item {
				color: rgba(108, 230, 216, 1);
			}

			.brz .brz-css-1kwae1d.brz-mm-menu .brz-menu__item .brz-mm-listitem__text {
				padding: 15px 20px 15px 20px;
			}

			.brz .brz-css-1kwae1d .brz-menu__item:hover>.brz-mm-listitem__text {
				color: rgba(108, 230, 216, 1);
			}

			.brz .brz-css-1kwae1d .brz-menu__item .brz-mm-menu__item__icon.brz-icon-svg-custom {
				background-color: rgba(108, 230, 216, 1);
			}

			.brz .brz-css-1kwae1d .brz-mm-navbar {
				color: rgba(108, 230, 216, 1);
			}

			.brz .brz-css-1kwae1d .brz-menu__item.brz-mm-listitem_opened {
				color: rgba(108, 230, 216, 1);
			}

			.brz .brz-css-1kwae1d .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {
				color: rgba(var(--brz-global-color8), 1);
			}

			.brz .brz-css-1kwae1d .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active):hover>.brz-mm-listitem__text {
				color: rgba(var(--brz-global-color8), 1);
			}

			.brz .brz-css-1kwae1d .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active)>.brz-mm-listitem__text>.brz-icon-svg.brz-icon-svg-custom {
				background-color: rgba(var(--brz-global-color8), 1);
			}
		}

		@media (max-width:991px) and (min-width:768px) {
			.brz .brz-css-1kwae1d .brz-menu__item {
				font-size: var(--brz-heading5tabletfontsize, initial);
				font-weight: var(--brz-heading5tabletfontweight, initial);
				font-weight: var(--brz-heading5tabletbold, initial);
				line-height: var(--brz-heading5tabletlineheight, initial);
				letter-spacing: var(--brz-heading5tabletletterspacing, initial);
				font-variation-settings: var(--brz-heading5tabletfontvariation, initial);
				font-style: var(--brz-heading5tabletitalic, initial);
				text-decoration: var(--brz-heading5tablettextdecoration, initial) !important;
				text-transform: var(--brz-heading5tablettexttransform, initial) !important;
			}

			.brz .brz-css-1kwae1d .brz-menu__item .brz-a {
				justify-content: center;
				text-align: center;
			}

			.brz .brz-css-1kwae1d .brz-mm-menu__item__icon {
				margin: 0 4px 0 0;
				font-size: 20px;
			}

			.brz .brz-css-1kwae1d .brz-mm-navbar {
				font-family: var(--brz-heading5fontfamily, initial);
				font-size: var(--brz-heading5tabletfontsize, initial);
				font-weight: var(--brz-heading5tabletfontweight, initial);
				font-weight: var(--brz-heading5tabletbold, initial);
				line-height: var(--brz-heading5tabletlineheight, initial);
				letter-spacing: var(--brz-heading5tabletletterspacing, initial);
				font-variation-settings: var(--brz-heading5tabletfontvariation, initial);
				font-style: var(--brz-heading5tabletitalic, initial);
				text-decoration: var(--brz-heading5tablettextdecoration, initial) !important;
				text-transform: var(--brz-heading5tablettexttransform, initial) !important;
				border-color: rgba(var(--brz-global-color6), 1);
			}

			.brz .brz-css-1kwae1d.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {
				height: calc(var(--brz-heading5tabletlineheight, initial) * var(--brz-heading5tabletfontsize, initial) + 15px + 15px);
				padding-right: 20px;
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-1kwae1d .brz-menu__item {
				font-family: var(--brz-ugudlcdcxlbqfontfamily, initial);
			}

			.brz .brz-css-1kwae1d .brz-menu__item {
				color: rgba(130, 247, 234, 1);
			}

			.brz .brz-css-1kwae1d.brz-mm-menu .brz-menu__item .brz-mm-listitem__text {
				padding: 15px 20px 15px 20px;
			}

			.brz .brz-css-1kwae1d .brz-menu__item:hover>.brz-mm-listitem__text {
				color: rgba(130, 247, 234, 1);
			}

			.brz .brz-css-1kwae1d .brz-menu__item .brz-mm-menu__item__icon.brz-icon-svg-custom {
				background-color: rgba(130, 247, 234, 1);
			}

			.brz .brz-css-1kwae1d .brz-mm-navbar {
				color: rgba(130, 247, 234, 1);
			}

			.brz .brz-css-1kwae1d .brz-menu__item.brz-mm-listitem_opened {
				color: rgba(130, 247, 234, 1);
			}

			.brz .brz-css-1kwae1d .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {
				color: rgba(255, 255, 255, 1);
			}

			.brz .brz-css-1kwae1d .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active):hover>.brz-mm-listitem__text {
				color: rgba(255, 255, 255, 1);
			}

			.brz .brz-css-1kwae1d .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active)>.brz-mm-listitem__text>.brz-icon-svg.brz-icon-svg-custom {
				background-color: rgba(255, 255, 255, 1);
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-1kwae1d .brz-menu__item {
				font-size: var(--brz-ugudlcdcxlbqmobilefontsize, initial);
				font-weight: var(--brz-ugudlcdcxlbqmobilefontweight, initial);
				font-weight: var(--brz-ugudlcdcxlbqmobilebold, initial);
				line-height: var(--brz-ugudlcdcxlbqmobilelineheight, initial);
				letter-spacing: var(--brz-ugudlcdcxlbqmobileletterspacing, initial);
				font-variation-settings: var(--brz-ugudlcdcxlbqmobilefontvariation, initial);
				font-style: var(--brz-ugudlcdcxlbqmobileitalic, initial);
				text-decoration: var(--brz-ugudlcdcxlbqmobiletextdecoration, initial) !important;
				text-transform: var(--brz-ugudlcdcxlbqmobiletexttransform, initial) !important;
			}

			.brz .brz-css-1kwae1d .brz-menu__item .brz-a {
				justify-content: center;
				text-align: center;
			}

			.brz .brz-css-1kwae1d .brz-mm-menu__item__icon {
				margin: 0 4px 0 0;
				font-size: 20px;
			}

			.brz .brz-css-1kwae1d .brz-mm-navbar {
				font-family: var(--brz-ugudlcdcxlbqfontfamily, initial);
				font-size: var(--brz-ugudlcdcxlbqmobilefontsize, initial);
				font-weight: var(--brz-ugudlcdcxlbqmobilefontweight, initial);
				font-weight: var(--brz-ugudlcdcxlbqmobilebold, initial);
				line-height: var(--brz-ugudlcdcxlbqmobilelineheight, initial);
				letter-spacing: var(--brz-ugudlcdcxlbqmobileletterspacing, initial);
				font-variation-settings: var(--brz-ugudlcdcxlbqmobilefontvariation, initial);
				font-style: var(--brz-ugudlcdcxlbqmobileitalic, initial);
				text-decoration: var(--brz-ugudlcdcxlbqmobiletextdecoration, initial) !important;
				text-transform: var(--brz-ugudlcdcxlbqmobiletexttransform, initial) !important;
				border-color: rgba(var(--brz-global-color6), 1);
			}

			.brz .brz-css-1kwae1d.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {
				height: calc(var(--brz-ugudlcdcxlbqmobilelineheight, initial) * var(--brz-ugudlcdcxlbqmobilefontsize, initial) + 15px + 15px);
				padding-right: 20px;
			}
		}

		.brz .brz-css-jow5dd {
			flex: 1 1 6.7%;
			max-width: 6.7%;
			justify-content: center;
		}

		.brz .brz-css-jow5dd .brz-columns__scroll-effect {
			justify-content: center;
		}

		@media (min-width:991px) {
			.brz .brz-css-jow5dd {
				z-index: auto;
				flex: 1 1 6.7%;
				max-width: 6.7%;
				justify-content: center;
			}

			.brz .brz-css-jow5dd .brz-columns__scroll-effect {
				justify-content: center;
			}

			.brz .brz-css-jow5dd>.brz-bg {
				margin: 0;
				mix-blend-mode: normal;
				border-radius: 0px;
			}

			.brz .brz-css-jow5dd:hover>.brz-bg {
				border: 0px solid rgba(102, 115, 141, 0);
				box-shadow: none;
			}

			.brz .brz-css-jow5dd>.brz-bg>.brz-bg-image {
				background-size: cover;
				background-repeat: no-repeat;
				-webkit-mask-image: none;
				mask-image: none;
			}

			.brz .brz-css-jow5dd:hover>.brz-bg>.brz-bg-image {
				background-image: none;
				filter: none;
				display: block;
			}

			.brz .brz-css-jow5dd:hover>.brz-bg>.brz-bg-image:after {
				content: "";
				background-image: none;
			}

			.brz .brz-css-jow5dd>.brz-bg>.brz-bg-color {
				-webkit-mask-image: none;
				mask-image: none;
			}

			.brz .brz-css-jow5dd:hover>.brz-bg>.brz-bg-color {
				background-color: rgba(0, 0, 0, 0);
				background-image: none;
			}

			.brz .brz-css-jow5dd>.brz-bg>.brz-bg-map {
				display: none;
			}

			.brz .brz-css-jow5dd:hover>.brz-bg>.brz-bg-map {
				filter: none;
			}

			.brz .brz-css-jow5dd>.brz-bg>.brz-bg-video {
				display: none;
			}

			.brz .brz-css-jow5dd:hover>.brz-bg>.brz-bg-video {
				filter: none;
			}
		}

		@media (min-width:991px) {
			.brz .brz-css-jow5dd:hover>.brz-bg {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-jow5dd:hover>.brz-bg>.brz-bg-image {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}

			.brz .brz-css-jow5dd:hover>.brz-bg>.brz-bg-color {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}
		}

		@media (max-width:991px) and (min-width:768px) {
			.brz .brz-css-jow5dd>* {
				display: none;
			}

			.brz .brz-css-jow5dd>.brz-column__items {
				display: none;
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-jow5dd {
				flex: 1 1 100%;
				max-width: 100%;
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-jow5dd>* {
				display: none;
			}

			.brz .brz-css-jow5dd>.brz-column__items {
				display: none;
			}
		}

		.brz .brz-css-190lc1z {
			padding: 0;
		}

		@media (min-width:991px) {
			.brz .brz-css-190lc1z {
				z-index: auto;
				margin: 0;
				border: 0px solid transparent;
				padding: 0;
				min-height: 100%;
			}
		}

		@media (min-width:991px) {
			.brz .brz-css-190lc1z:hover {
				display: flex;
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}
		}

		@media (max-width:991px) and (min-width:768px) {
			.brz .brz-css-190lc1z {
				padding: 5px 15px 5px 15px;
			}
		}

		.brz .brz-css-xmdiqp {
			margin: 0;
		}

		.brz .brz-css-xmdiqp .brz-wrapper-transform {
			--transform-flipHorizontal: 1;
			--transform-flipVertical: 1;
			--transform-anchor-pointX: center;
			--transform-anchor-pointY: center;
			transform: scaleX(calc(var(--transform-flipHorizontal) * 1)) scaleY(calc(var(--transform-flipVertical) * 1));
			transform-origin: var(--transform-anchor-pointY) var(--transform-anchor-pointX);
		}

		@media (min-width:991px) {
			.brz .brz-css-xmdiqp {
				padding: 0;
				margin: 0;
				justify-content: center;
				position: relative;
			}

			.brz .brz-css-xmdiqp .brz-wrapper-transform {
				--transform-flipHorizontal: 1;
				--transform-flipVertical: 1;
				--transform-anchor-pointX: center;
				--transform-anchor-pointY: center;
				transform: scaleX(calc(var(--transform-flipHorizontal) * 1)) scaleY(calc(var(--transform-flipVertical) * 1));
				transform-origin: var(--transform-anchor-pointY) var(--transform-anchor-pointX);
			}
		}

		@media (min-width:991px) {
			.brz .brz-css-xmdiqp {
				display: flex;
				z-index: auto;
				position: relative;
			}
		}

		@media (max-width:991px) and (min-width:768px) {
			.brz .brz-css-xmdiqp {
				margin: 10px 0px 10px 0px;
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-xmdiqp {
				margin: 10px 0px 10px 0px;
			}
		}

		.brz .brz-css-nntapz {
			flex-direction: row;
		}

		.brz .brz-css-nntapz .brz-icon__container {
			margin-left: auto;
			margin-right: 20px;
			align-items: flex-start;
		}

		.brz .brz-css-1umndxb .brz-icon__container {
			margin-left: auto;
			margin-right: 2px;
		}

		@media (min-width:991px) {
			.brz .brz-css-1umndxb {
				flex-direction: row;
			}

			.brz .brz-css-1umndxb .brz-icon__container {
				margin-left: auto;
				margin-right: 2px;
				align-items: flex-start;
			}
		}

		.brz .brz-css-e9bk1k {
			font-size: 48px;
			padding: 0px;
			border-radius: 0;
			stroke-width: 1;
		}

		.brz .brz-css-e9bk1k {
			color: rgba(var(--brz-global-color3), 1);
			border: 0px solid rgba(35, 157, 219, 0);
			box-shadow: none;
			background-color: rgba(189, 225, 244, 0);
			background-image: none;
		}

		.brz .brz-css-e9bk1k .brz-icon-svg-custom {
			background-color: rgba(var(--brz-global-color3), 1);
		}

		@media (min-width:991px) {
			.brz .brz-css-e9bk1k {
				transition-duration: .5s;
				transition-property: color, box-shadow, background, border, border-color;
			}

			.brz .brz-css-e9bk1k .brz-icon-svg-custom {
				transition-duration: .5s;
				transition-property: color, box-shadow, background, border, border-color;
			}
		}

		@media (min-width:991px) {
			.brz .brz-css-e9bk1k:hover {
				color: rgba(var(--brz-global-color3), .8);
			}

			.brz .brz-css-e9bk1k:hover .brz-icon-svg-custom {
				background-color: rgba(var(--brz-global-color3), .8);
			}
		}

		@media (max-width:991px) and (min-width:768px) {
			.brz .brz-css-e9bk1k {
				transition-duration: .5s;
				transition-property: color, box-shadow, background, border, border-color;
			}

			.brz .brz-css-e9bk1k .brz-icon-svg-custom {
				transition-duration: .5s;
				transition-property: color, box-shadow, background, border, border-color;
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-e9bk1k {
				transition-duration: .5s;
				transition-property: color, box-shadow, background, border, border-color;
			}

			.brz .brz-css-e9bk1k .brz-icon-svg-custom {
				transition-duration: .5s;
				transition-property: color, box-shadow, background, border, border-color;
			}
		}

		.brz .brz-css-h3q0h5 {
			font-size: 20px;
		}

		@media (min-width:991px) {
			.brz .brz-css-h3q0h5 {
				font-size: 20px;
				padding: 0px;
				border-radius: 0;
				stroke-width: 1;
			}

			.brz .brz-css-h3q0h5:hover {
				border: 0px solid rgba(35, 157, 219, 0);
				box-shadow: none;
				background-color: rgba(189, 225, 244, 0);
				background-image: none;
			}
		}

		@media (min-width:991px) {
			.brz .brz-css-h3q0h5:hover {
				transition-duration: .5s;
				transition-property: color, box-shadow, background, border, border-color;
			}

			.brz .brz-css-h3q0h5:hover .brz-icon-svg-custom {
				transition-duration: .5s;
				transition-property: color, box-shadow, background, border, border-color;
			}
		}

		.brz .brz-css-10fnxcx {
			width: 100%;
			mix-blend-mode: normal;
		}

		@media (min-width:991px) {
			.brz .brz-css-1n6htfm {
				width: 100%;
				mix-blend-mode: normal;
			}
		}

		.brz .brz-css-aQaKb {
			margin-top: 0px !important;
			margin-bottom: 0px !important;
			text-align: justify !important;
			font-family: "Comfortaa", display !important;
			font-size: 16px;
			line-height: 1.7;
			font-weight: 400;
			letter-spacing: -.2px;
			font-variation-settings: "wght" 400, "wdth" 100, "SOFT" 0;
		}

		@media (min-width:991px) {
			.brz .brz-css-aQaKb {
				margin-top: 0px !important;
				margin-bottom: 0px !important;
				text-align: justify !important;
				font-family: "Comfortaa", display !important;
				font-size: 16px;
				line-height: 1.7;
				font-weight: 400;
				letter-spacing: -.2px;
				font-variation-settings: "wght" 400, "wdth" 100, "SOFT" 0;
				text-transform: inherit !important;
			}
		}

		.brz .brz-css-r8i1ey {
			margin: 0;
		}

		@media (min-width:991px) {
			.brz .brz-css-r8i1ey {
				padding: 0;
				margin: 0;
				justify-content: center;
				position: relative;
			}

			.brz .brz-css-r8i1ey .brz-wrapper-transform {
				transform: none;
			}
		}

		@media (min-width:991px) {
			.brz .brz-css-r8i1ey {
				display: flex;
				z-index: auto;
				position: relative;
			}
		}

		@media (max-width:991px) and (min-width:768px) {
			.brz .brz-css-r8i1ey {
				margin: 10px 0px 10px 0px;
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-r8i1ey {
				margin: 10px 0px 10px 0px;
			}
		}

		.brz .brz-css-zwba1n {
			width: 100%;
			min-height: 100%;
		}

		.brz .brz-css-zwba1n:before {
			border-radius: 0px;
		}

		.brz .brz-css-zwba1n:before {
			box-shadow: none;
			border: 0px solid rgba(220, 222, 225, 1);
		}

		.brz .brz-css-zwba1n .brz-embed-content {
			padding: 0;
			border-radius: 0px;
			overflow: hidden;
		}

		.brz .brz-css-zwba1n .brz-embed-content {
			background-color: rgba(var(--brz-global-color2), 0);
			background-image: none;
		}

		@media (min-width:991px) {
			.brz .brz-css-zwba1n:before {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}
		}

		@media (max-width:991px) and (min-width:768px) {
			.brz .brz-css-zwba1n:before {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}
		}

		@media (max-width:767px) {
			.brz .brz-css-zwba1n:before {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}
		}

		@media (min-width:991px) {
			.brz .brz-css-3bcg12 {
				width: 100%;
				min-height: 100%;
			}

			.brz .brz-css-3bcg12:before {
				border-radius: 0px;
			}

			.brz .brz-css-3bcg12:hover:before {
				box-shadow: none;
				border: 0px solid rgba(220, 222, 225, 1);
			}

			.brz .brz-css-3bcg12 .brz-embed-content {
				padding: 0;
				border-radius: 0px;
				overflow: hidden;
			}

			.brz .brz-css-3bcg12:hover .brz-embed-content {
				background-color: rgba(var(--brz-global-color2), 0);
				background-image: none;
			}
		}

		@media (min-width:991px) {
			.brz .brz-css-3bcg12:hover:before {
				transition-duration: .5s;
				transition-property: filter, box-shadow, background, border-radius, border-color;
			}
		}
	</style>
	<style class="brz-style brz-project__style-palette">
		.brz .brz-cp-color1,
		.brz .brz-bcp-color1 {
			color: rgb(var(--brz-global-color1));
		}

		.brz .brz-bgp-color1 {
			background-color: rgb(var(--brz-global-color1));
		}

		.brz .brz-cp-color2,
		.brz .brz-bcp-color2 {
			color: rgb(var(--brz-global-color2));
		}

		.brz .brz-bgp-color2 {
			background-color: rgb(var(--brz-global-color2));
		}

		.brz .brz-cp-color3,
		.brz .brz-bcp-color3 {
			color: rgb(var(--brz-global-color3));
		}

		.brz .brz-bgp-color3 {
			background-color: rgb(var(--brz-global-color3));
		}

		.brz .brz-cp-color4,
		.brz .brz-bcp-color4 {
			color: rgb(var(--brz-global-color4));
		}

		.brz .brz-bgp-color4 {
			background-color: rgb(var(--brz-global-color4));
		}

		.brz .brz-cp-color5,
		.brz .brz-bcp-color5 {
			color: rgb(var(--brz-global-color5));
		}

		.brz .brz-bgp-color5 {
			background-color: rgb(var(--brz-global-color5));
		}

		.brz .brz-cp-color6,
		.brz .brz-bcp-color6 {
			color: rgb(var(--brz-global-color6));
		}

		.brz .brz-bgp-color6 {
			background-color: rgb(var(--brz-global-color6));
		}

		.brz .brz-cp-color7,
		.brz .brz-bcp-color7 {
			color: rgb(var(--brz-global-color7));
		}

		.brz .brz-bgp-color7 {
			background-color: rgb(var(--brz-global-color7));
		}

		.brz .brz-cp-color8,
		.brz .brz-bcp-color8 {
			color: rgb(var(--brz-global-color8));
		}

		.brz .brz-bgp-color8 {
			background-color: rgb(var(--brz-global-color8));
		}

		:root {
			--brz-global-color1: 114, 174, 172;
			--brz-global-color2: 50, 158, 155;
			--brz-global-color3: 114, 174, 172;
			--brz-global-color4: 184, 230, 225;
			--brz-global-color5: 84, 84, 84;
			--brz-global-color6: 238, 243, 240;
			--brz-global-color7: 103, 115, 108;
			--brz-global-color8: 255, 255, 255;
		}

		:root {
			--brz-paragraphfontfamily: "Comfortaa", display;
			--brz-paragraphfontsize: 18px;
			--brz-paragraphfontsizesuffix: px;
			--brz-paragraphfontweight: 300;
			--brz-paragraphletterspacing: -.2px;
			--brz-paragraphlineheight: 1.4;
			--brz-paragraphfontvariation: "wght" 400, "wdth" 100, "SOFT" 0;
			--brz-paragraphtabletfontsize: 17px;
			--brz-paragraphtabletfontweight: 100;
			--brz-paragraphtabletletterspacing: 0px;
			--brz-paragraphtabletlineheight: 1.4;
			--brz-paragraphtabletfontvariation: "wght" 400, "wdth" 100, "SOFT" 0;
			--brz-paragraphmobilefontsize: 16px;
			--brz-paragraphmobilefontweight: 100;
			--brz-paragraphmobileletterspacing: -.2px;
			--brz-paragraphmobilelineheight: 1.4;
			--brz-paragraphmobilefontvariation: "wght" 400, "wdth" 100, "SOFT" 0;
			--brz-paragraphstoryfontsize: 4.14%;
			--brz-paragraphbold: 300;
			--brz-paragraphitalic: inherit;
			--brz-paragraphtextdecoration: inherit;
			--brz-paragraphtexttransform: inherit;
			--brz-paragraphtabletbold: 100;
			--brz-paragraphtabletitalic: inherit;
			--brz-paragraphtablettextdecoration: inherit;
			--brz-paragraphtablettexttransform: inherit;
			--brz-paragraphmobilebold: 100;
			--brz-paragraphmobileitalic: inherit;
			--brz-paragraphmobiletextdecoration: inherit;
			--brz-paragraphmobiletexttransform: inherit;
			--brz-subtitlefontfamily: "Comfortaa", display;
			--brz-subtitlefontsize: 24px;
			--brz-subtitlefontsizesuffix: px;
			--brz-subtitlefontweight: 400;
			--brz-subtitleletterspacing: -.2px;
			--brz-subtitlelineheight: 1.4;
			--brz-subtitlefontvariation: "wght" 400, "wdth" 100, "SOFT" 0;
			--brz-subtitletabletfontsize: 20px;
			--brz-subtitletabletfontweight: 100;
			--brz-subtitletabletletterspacing: 0px;
			--brz-subtitletabletlineheight: 1.4;
			--brz-subtitletabletfontvariation: "wght" 400, "wdth" 100, "SOFT" 0;
			--brz-subtitlemobilefontsize: 16px;
			--brz-subtitlemobilefontweight: 100;
			--brz-subtitlemobileletterspacing: 0px;
			--brz-subtitlemobilelineheight: 1.4;
			--brz-subtitlemobilefontvariation: "wght" 400, "wdth" 100, "SOFT" 0;
			--brz-subtitlestoryfontsize: 5.52%;
			--brz-subtitlebold: 400;
			--brz-subtitleitalic: inherit;
			--brz-subtitletextdecoration: inherit;
			--brz-subtitletexttransform: inherit;
			--brz-subtitletabletbold: 100;
			--brz-subtitletabletitalic: inherit;
			--brz-subtitletablettextdecoration: inherit;
			--brz-subtitletablettexttransform: inherit;
			--brz-subtitlemobilebold: 100;
			--brz-subtitlemobileitalic: inherit;
			--brz-subtitlemobiletextdecoration: inherit;
			--brz-subtitlemobiletexttransform: inherit;
			--brz-abovetitlefontfamily: "Comfortaa", display;
			--brz-abovetitlefontsize: 20px;
			--brz-abovetitlefontsizesuffix: px;
			--brz-abovetitlefontweight: 400;
			--brz-abovetitleletterspacing: 0px;
			--brz-abovetitlelineheight: 1.4;
			--brz-abovetitlefontvariation: "wght" 400, "wdth" 100, "SOFT" 0;
			--brz-abovetitletabletfontsize: 18px;
			--brz-abovetitletabletfontweight: 400;
			--brz-abovetitletabletletterspacing: 0px;
			--brz-abovetitletabletlineheight: 1.4;
			--brz-abovetitletabletfontvariation: "wght" 400, "wdth" 100, "SOFT" 0;
			--brz-abovetitlemobilefontsize: 18px;
			--brz-abovetitlemobilefontweight: 400;
			--brz-abovetitlemobileletterspacing: 0px;
			--brz-abovetitlemobilelineheight: 1.4;
			--brz-abovetitlemobilefontvariation: "wght" 400, "wdth" 100, "SOFT" 0;
			--brz-abovetitlestoryfontsize: 4.6%;
			--brz-abovetitlebold: 400;
			--brz-abovetitleitalic: inherit;
			--brz-abovetitletextdecoration: inherit;
			--brz-abovetitletexttransform: inherit;
			--brz-abovetitletabletbold: 400;
			--brz-abovetitletabletitalic: inherit;
			--brz-abovetitletablettextdecoration: inherit;
			--brz-abovetitletablettexttransform: inherit;
			--brz-abovetitlemobilebold: 400;
			--brz-abovetitlemobileitalic: inherit;
			--brz-abovetitlemobiletextdecoration: inherit;
			--brz-abovetitlemobiletexttransform: inherit;
			--brz-heading1fontfamily: "Comfortaa", display;
			--brz-heading1fontsize: 54px;
			--brz-heading1fontsizesuffix: px;
			--brz-heading1fontweight: 700;
			--brz-heading1letterspacing: -.2px;
			--brz-heading1lineheight: 1.2;
			--brz-heading1fontvariation: "wght" 400, "wdth" 100, "SOFT" 0;
			--brz-heading1tabletfontsize: 40px;
			--brz-heading1tabletfontweight: 700;
			--brz-heading1tabletletterspacing: -.2px;
			--brz-heading1tabletlineheight: 1.2;
			--brz-heading1tabletfontvariation: "wght" 400, "wdth" 100, "SOFT" 0;
			--brz-heading1mobilefontsize: 28px;
			--brz-heading1mobilefontweight: 700;
			--brz-heading1mobileletterspacing: -.8px;
			--brz-heading1mobilelineheight: 1.2;
			--brz-heading1mobilefontvariation: "wght" 400, "wdth" 100, "SOFT" 0;
			--brz-heading1storyfontsize: 12.42%;
			--brz-heading1bold: 700;
			--brz-heading1italic: inherit;
			--brz-heading1textdecoration: inherit;
			--brz-heading1texttransform: inherit;
			--brz-heading1tabletbold: 700;
			--brz-heading1tabletitalic: inherit;
			--brz-heading1tablettextdecoration: inherit;
			--brz-heading1tablettexttransform: inherit;
			--brz-heading1mobilebold: 700;
			--brz-heading1mobileitalic: inherit;
			--brz-heading1mobiletextdecoration: inherit;
			--brz-heading1mobiletexttransform: inherit;
			--brz-heading2fontfamily: "Comfortaa", display;
			--brz-heading2fontsize: 36px;
			--brz-heading2fontsizesuffix: px;
			--brz-heading2fontweight: 700;
			--brz-heading2letterspacing: -.8px;
			--brz-heading2lineheight: 1.2;
			--brz-heading2fontvariation: "wght" 400, "wdth" 100, "SOFT" 0;
			--brz-heading2tabletfontsize: 34px;
			--brz-heading2tabletfontweight: 700;
			--brz-heading2tabletletterspacing: -.8px;
			--brz-heading2tabletlineheight: 1.2;
			--brz-heading2tabletfontvariation: "wght" 400, "wdth" 100, "SOFT" 0;
			--brz-heading2mobilefontsize: 22px;
			--brz-heading2mobilefontweight: 700;
			--brz-heading2mobileletterspacing: -1px;
			--brz-heading2mobilelineheight: 1.2;
			--brz-heading2mobilefontvariation: "wght" 400, "wdth" 100, "SOFT" 0;
			--brz-heading2storyfontsize: 8.28%;
			--brz-heading2bold: 700;
			--brz-heading2italic: inherit;
			--brz-heading2textdecoration: inherit;
			--brz-heading2texttransform: inherit;
			--brz-heading2tabletbold: 700;
			--brz-heading2tabletitalic: inherit;
			--brz-heading2tablettextdecoration: inherit;
			--brz-heading2tablettexttransform: inherit;
			--brz-heading2mobilebold: 700;
			--brz-heading2mobileitalic: inherit;
			--brz-heading2mobiletextdecoration: inherit;
			--brz-heading2mobiletexttransform: inherit;
			--brz-heading3fontfamily: "Comfortaa", display;
			--brz-heading3fontsize: 22px;
			--brz-heading3fontsizesuffix: px;
			--brz-heading3fontweight: 400;
			--brz-heading3letterspacing: -.6px;
			--brz-heading3lineheight: 1.4;
			--brz-heading3fontvariation: "wght" 400, "wdth" 100, "SOFT" 0;
			--brz-heading3tabletfontsize: 28px;
			--brz-heading3tabletfontweight: 200;
			--brz-heading3tabletletterspacing: -.2px;
			--brz-heading3tabletlineheight: 1.3;
			--brz-heading3tabletfontvariation: "wght" 400, "wdth" 100, "SOFT" 0;
			--brz-heading3mobilefontsize: 20px;
			--brz-heading3mobilefontweight: 200;
			--brz-heading3mobileletterspacing: -.8px;
			--brz-heading3mobilelineheight: 1.3;
			--brz-heading3mobilefontvariation: "wght" 400, "wdth" 100, "SOFT" 0;
			--brz-heading3storyfontsize: 5.06%;
			--brz-heading3bold: 400;
			--brz-heading3italic: inherit;
			--brz-heading3textdecoration: inherit;
			--brz-heading3texttransform: inherit;
			--brz-heading3tabletbold: 200;
			--brz-heading3tabletitalic: inherit;
			--brz-heading3tablettextdecoration: inherit;
			--brz-heading3tablettexttransform: inherit;
			--brz-heading3mobilebold: 200;
			--brz-heading3mobileitalic: inherit;
			--brz-heading3mobiletextdecoration: inherit;
			--brz-heading3mobiletexttransform: inherit;
			--brz-heading4fontfamily: "Comfortaa", display;
			--brz-heading4fontsize: 18px;
			--brz-heading4fontsizesuffix: px;
			--brz-heading4fontweight: 700;
			--brz-heading4letterspacing: -.2px;
			--brz-heading4lineheight: 1.4;
			--brz-heading4fontvariation: "wght" 400, "wdth" 100, "SOFT" 0;
			--brz-heading4tabletfontsize: 17px;
			--brz-heading4tabletfontweight: 700;
			--brz-heading4tabletletterspacing: 0px;
			--brz-heading4tabletlineheight: 1.4;
			--brz-heading4tabletfontvariation: "wght" 400, "wdth" 100, "SOFT" 0;
			--brz-heading4mobilefontsize: 17px;
			--brz-heading4mobilefontweight: 700;
			--brz-heading4mobileletterspacing: 0px;
			--brz-heading4mobilelineheight: 1.4;
			--brz-heading4mobilefontvariation: "wght" 400, "wdth" 100, "SOFT" 0;
			--brz-heading4storyfontsize: 4.14%;
			--brz-heading4bold: 700;
			--brz-heading4italic: inherit;
			--brz-heading4textdecoration: inherit;
			--brz-heading4texttransform: inherit;
			--brz-heading4tabletbold: 700;
			--brz-heading4tabletitalic: inherit;
			--brz-heading4tablettextdecoration: inherit;
			--brz-heading4tablettexttransform: inherit;
			--brz-heading4mobilebold: 700;
			--brz-heading4mobileitalic: inherit;
			--brz-heading4mobiletextdecoration: inherit;
			--brz-heading4mobiletexttransform: inherit;
			--brz-heading5fontfamily: "Comfortaa", display;
			--brz-heading5fontsize: 16px;
			--brz-heading5fontsizesuffix: px;
			--brz-heading5fontweight: 400;
			--brz-heading5letterspacing: 0px;
			--brz-heading5lineheight: 1.4;
			--brz-heading5fontvariation: "wght" 400, "wdth" 100, "SOFT" 0;
			--brz-heading5tabletfontsize: 16px;
			--brz-heading5tabletfontweight: 400;
			--brz-heading5tabletletterspacing: 0px;
			--brz-heading5tabletlineheight: 1.4;
			--brz-heading5tabletfontvariation: "wght" 400, "wdth" 100, "SOFT" 0;
			--brz-heading5mobilefontsize: 16px;
			--brz-heading5mobilefontweight: 400;
			--brz-heading5mobileletterspacing: 0px;
			--brz-heading5mobilelineheight: 1.4;
			--brz-heading5mobilefontvariation: "wght" 400, "wdth" 100, "SOFT" 0;
			--brz-heading5storyfontsize: 3.68%;
			--brz-heading5bold: 400;
			--brz-heading5italic: inherit;
			--brz-heading5textdecoration: inherit;
			--brz-heading5texttransform: inherit;
			--brz-heading5tabletbold: 400;
			--brz-heading5tabletitalic: inherit;
			--brz-heading5tablettextdecoration: inherit;
			--brz-heading5tablettexttransform: inherit;
			--brz-heading5mobilebold: 400;
			--brz-heading5mobileitalic: inherit;
			--brz-heading5mobiletextdecoration: inherit;
			--brz-heading5mobiletexttransform: inherit;
			--brz-heading6fontfamily: "Comfortaa", display;
			--brz-heading6fontsize: 14px;
			--brz-heading6fontsizesuffix: px;
			--brz-heading6fontweight: 300;
			--brz-heading6letterspacing: -.4px;
			--brz-heading6lineheight: 1.5;
			--brz-heading6fontvariation: "wght" 400, "wdth" 100, "SOFT" 0;
			--brz-heading6tabletfontsize: 14px;
			--brz-heading6tabletfontweight: 400;
			--brz-heading6tabletletterspacing: 0px;
			--brz-heading6tabletlineheight: 1.5;
			--brz-heading6tabletfontvariation: "wght" 400, "wdth" 100, "SOFT" 0;
			--brz-heading6mobilefontsize: 14px;
			--brz-heading6mobilefontweight: 400;
			--brz-heading6mobileletterspacing: 0px;
			--brz-heading6mobilelineheight: 1.5;
			--brz-heading6mobilefontvariation: "wght" 400, "wdth" 100, "SOFT" 0;
			--brz-heading6storyfontsize: 3.22%;
			--brz-heading6bold: 300;
			--brz-heading6italic: inherit;
			--brz-heading6textdecoration: inherit;
			--brz-heading6texttransform: inherit;
			--brz-heading6tabletbold: 400;
			--brz-heading6tabletitalic: inherit;
			--brz-heading6tablettextdecoration: inherit;
			--brz-heading6tablettexttransform: inherit;
			--brz-heading6mobilebold: 400;
			--brz-heading6mobileitalic: inherit;
			--brz-heading6mobiletextdecoration: inherit;
			--brz-heading6mobiletexttransform: inherit;
			--brz-buttonfontfamily: "Comfortaa", display;
			--brz-buttonfontsize: 18px;
			--brz-buttonfontsizesuffix: px;
			--brz-buttonfontweight: 700;
			--brz-buttonletterspacing: -.2px;
			--brz-buttonlineheight: 1.6;
			--brz-buttonfontvariation: "wght" 400, "wdth" 100, "SOFT" 0;
			--brz-buttontabletfontsize: 16px;
			--brz-buttontabletfontweight: 700;
			--brz-buttontabletletterspacing: 0px;
			--brz-buttontabletlineheight: 1.6;
			--brz-buttontabletfontvariation: "wght" 400, "wdth" 100, "SOFT" 0;
			--brz-buttonmobilefontsize: 16px;
			--brz-buttonmobilefontweight: 700;
			--brz-buttonmobileletterspacing: 0px;
			--brz-buttonmobilelineheight: 1.6;
			--brz-buttonmobilefontvariation: "wght" 400, "wdth" 100, "SOFT" 0;
			--brz-buttonstoryfontsize: 4.14%;
			--brz-buttonbold: 700;
			--brz-buttonitalic: inherit;
			--brz-buttontextdecoration: inherit;
			--brz-buttontexttransform: inherit;
			--brz-buttontabletbold: 700;
			--brz-buttontabletitalic: inherit;
			--brz-buttontablettextdecoration: inherit;
			--brz-buttontablettexttransform: inherit;
			--brz-buttonmobilebold: 700;
			--brz-buttonmobileitalic: inherit;
			--brz-buttonmobiletextdecoration: inherit;
			--brz-buttonmobiletexttransform: inherit;
			--brz-ugudlcdcxlbqfontfamily: "Comfortaa", display;
			--brz-ugudlcdcxlbqfontsize: 16px;
			--brz-ugudlcdcxlbqfontsizesuffix: px;
			--brz-ugudlcdcxlbqfontweight: 400;
			--brz-ugudlcdcxlbqletterspacing: -.2px;
			--brz-ugudlcdcxlbqlineheight: 1.4;
			--brz-ugudlcdcxlbqfontvariation: "wght" 400, "wdth" 100, "SOFT" 0;
			--brz-ugudlcdcxlbqtabletfontsize: 15px;
			--brz-ugudlcdcxlbqtabletfontweight: 400;
			--brz-ugudlcdcxlbqtabletletterspacing: -.2px;
			--brz-ugudlcdcxlbqtabletlineheight: 1.5;
			--brz-ugudlcdcxlbqtabletfontvariation: "wght" 400, "wdth" 100, "SOFT" 0;
			--brz-ugudlcdcxlbqmobilefontsize: 15px;
			--brz-ugudlcdcxlbqmobilefontweight: 400;
			--brz-ugudlcdcxlbqmobileletterspacing: -.2px;
			--brz-ugudlcdcxlbqmobilelineheight: 1.4;
			--brz-ugudlcdcxlbqmobilefontvariation: "wght" 400, "wdth" 100, "SOFT" 0;
			--brz-ugudlcdcxlbqstoryfontsize: 3.68%;
			--brz-ugudlcdcxlbqbold: 400;
			--brz-ugudlcdcxlbqitalic: inherit;
			--brz-ugudlcdcxlbqtextdecoration: inherit;
			--brz-ugudlcdcxlbqtexttransform: inherit;
			--brz-ugudlcdcxlbqtabletbold: 400;
			--brz-ugudlcdcxlbqtabletitalic: inherit;
			--brz-ugudlcdcxlbqtablettextdecoration: inherit;
			--brz-ugudlcdcxlbqtablettexttransform: inherit;
			--brz-ugudlcdcxlbqmobilebold: 400;
			--brz-ugudlcdcxlbqmobileitalic: inherit;
			--brz-ugudlcdcxlbqmobiletextdecoration: inherit;
			--brz-ugudlcdcxlbqmobiletexttransform: inherit;
			--brz-u9xihr8qxhssfontfamily: "Comfortaa", display;
			--brz-u9xihr8qxhssfontsize: 14px;
			--brz-u9xihr8qxhssfontsizesuffix: px;
			--brz-u9xihr8qxhssfontweight: 700;
			--brz-u9xihr8qxhssletterspacing: -.2px;
			--brz-u9xihr8qxhsslineheight: 1.4;
			--brz-u9xihr8qxhssfontvariation: "wght" 400, "wdth" 100, "SOFT" 0;
			--brz-u9xihr8qxhsstabletfontsize: 14px;
			--brz-u9xihr8qxhsstabletfontweight: 400;
			--brz-u9xihr8qxhsstabletletterspacing: -.3px;
			--brz-u9xihr8qxhsstabletlineheight: 1.2;
			--brz-u9xihr8qxhsstabletfontvariation: "wght" 400, "wdth" 100, "SOFT" 0;
			--brz-u9xihr8qxhssmobilefontsize: 13px;
			--brz-u9xihr8qxhssmobilefontweight: 700;
			--brz-u9xihr8qxhssmobileletterspacing: -.4px;
			--brz-u9xihr8qxhssmobilelineheight: 1.3;
			--brz-u9xihr8qxhssmobilefontvariation: "wght" 400, "wdth" 100, "SOFT" 0;
			--brz-u9xihr8qxhssstoryfontsize: 3.22%;
			--brz-u9xihr8qxhssbold: 700;
			--brz-u9xihr8qxhssitalic: inherit;
			--brz-u9xihr8qxhsstextdecoration: inherit;
			--brz-u9xihr8qxhsstexttransform: inherit;
			--brz-u9xihr8qxhsstabletbold: 400;
			--brz-u9xihr8qxhsstabletitalic: inherit;
			--brz-u9xihr8qxhsstablettextdecoration: inherit;
			--brz-u9xihr8qxhsstablettexttransform: inherit;
			--brz-u9xihr8qxhssmobilebold: 700;
			--brz-u9xihr8qxhssmobileitalic: inherit;
			--brz-u9xihr8qxhssmobiletextdecoration: inherit;
			--brz-u9xihr8qxhssmobiletexttransform: inherit;
		}
	</style>
	<script src="../assets/bf2ef77c3f1c0ffec9f3bff8f927b13f.js" class="brz-script brz-script-preview-lib" defer="true"
		data-brz-group="group-jq"></script>
	<script src="../assets/7bd5bb61c3a6cf37aef2a8ce02be7461.js" class="brz-script brz-script-preview-lib-pro" defer="true"
		data-brz-group="group-1_2"></script>
	<script src="../assets/246b62aec7f6f35cef79e957ee00b9c9.js" class="brz-script brz-script-preview-pro"
		defer="true"></script>
</head>

<body class="brz">
	<div class="brz-root__container brz-reset-all brz brz-root__container-page">
		<section id="i87b3083ec1ef774a32e2_add3583050b76c25118a9b567f3238c29"
			class="brz-section brz-section__header brz-css-sk5zf3 brz-css-c8fq4o">
			<div class="brz-section__menu-item brz-css-1wzn4p1 brz-css-1uopts9"
				data-brz-custom-id="pmjtztsvmmgucjewowvarmptbegnhqkrygyg">
				<div class="brz-container brz-css-2gpbzd brz-css-1efqzy4">
					<header class="brz-row__container brz-css-1t66ezt brz-css-1a7ogzm"
						data-brz-custom-id="qxrqcghhpyzdntkwxqtwalcnjpphwfwfpnai">
						<div class="brz-bg">
							<div class="brz-bg-color"></div>
						</div>
						<div class="brz-row brz-css-lt4404 brz-css-18oneyz">
							<div class="brz-columns brz-css-1csnpdv brz-css-88dicl"
								data-brz-custom-id="mxbfnikhdyxqeblxvgyysleuexbinafgdwmy">
								<div class="brz-column__items brz-css-1pzte4r brz-css-znagm0">
									<div id="" class="brz-css-1tjbyob brz-css-1puqgto brz-wrapper">
										<div class="brz-image brz-css-q31jzm brz-css-yp9xav"
											data-brz-custom-id="ozphomskbadqfdvikpfdqyfniixzvkbtxcbk"><a class="brz-a"
												href="/" target="_self" rel="noopener" data-brz-link-type="external">
												<picture
													class="brz-picture brz-d-block brz-p-relative brz-css-1yykq65 brz-css-z4zlrs">
													<source
														srcset="../assets/5bd10bf04072b5d66d2acac718fe483e.webp 1x, ../assets/5bd10bf04072b5d66d2acac718fe483e.webp 2x"
														media="(min-width: 992px)">
													<source
														srcset="../assets/5bd10bf04072b5d66d2acac718fe483e.webp 1x, ../assets/5bd10bf04072b5d66d2acac718fe483e.webp 2x"
														media="(min-width: 768px)"><img class="brz-img"
														srcset="../assets/5bd10bf04072b5d66d2acac718fe483e.webp 1x, ../assets/5bd10bf04072b5d66d2acac718fe483e.webp 2x"
														src="../assets/5bd10bf04072b5d66d2acac718fe483e.webp" alt=""
														title="cymta-logo-el.webp" draggable="false" loading="lazy">
												</picture>
											</a></div>
									</div>
								</div>
							</div>
							<div class="brz-columns brz-css-1csnpdv brz-css-1t2tmqk"
								data-brz-custom-id="lvqsghryjlhtilcubdlpnhcdhvaftslruozx">
								<div class="brz-column__items brz-css-1pzte4r brz-css-kmg040">
									<div id="" class="brz-css-1tjbyob brz-css-icg1tg brz-wrapper">
										<div class="brz-menu__container brz-css-19uzdaw brz-css-159oqwi"
											data-mmenu-id="#fbiyjwwellhvtbfzvawrysdglhnvhbgmljyw_ie915c2c30027576af07b"
											data-mmenu-position="position-left" data-mmenu-title="Menu"
											data-mmenu-stickytitle="on"
											data-mmenu-closingicon="%7B%22desktop%22%3A%22off%22%2C%22tablet%22%3A%22off%22%2C%22mobile%22%3A%22off%22%7D"
											data-brz-custom-id="fbiyjwwellhvtbfzvawrysdglhnvhbgmljyw">
											<nav data-mods="%7B%22desktop%22%3A%22horizontal%22%2C%22tablet%22%3A%22horizontal%22%2C%22mobile%22%3A%22horizontal%22%7D"
												class="brz-menu brz-menu__preview brz-css-c39cel brz-css-1tdvdmo">
												<ul class="brz-menu__ul">
													<li data-menu-item-id="b22b960322341a5e734d1324bbd76ed2"
														class="brz-menu__item"><a class="brz-a" target="" href="/mt"
															title="Μουσικοθεραπεία"><span
																class="brz-span">Μουσικοθεραπεία</span></a>
														<ul class="brz-menu__sub-menu">
															<li data-menu-item-id="01d48bae25f7229912cdc6bd654489a6"
																class="brz-menu__item"><a class="brz-a" target=""
																	href="/find" title="Βρες Μουσικοθεραπευτή"><span
																		class="brz-span">Βρες
																		Μουσικοθεραπευτή</span></a></li>
															<li data-menu-item-id="ead41e4a9b5e03ba07aeb9e426f4c21a"
																class="brz-menu__item"><a class="brz-a" target=""
																	href="/study" title="Σπουδές Μουσικοθεραπείας"><span
																		class="brz-span">Σπουδές
																		Μουσικοθεραπείας</span></a></li>
															<li data-menu-item-id="602acc623566a539fa600e1a24add4a0"
																class="brz-menu__item"><a class="brz-a" target=""
																	href="/mt-info"
																	title="Πληροφορίες για Μουσικοθεραπευτές"><span
																		class="brz-span">Πληροφορίες για
																		Μουσικοθεραπευτές</span></a></li>
														</ul>
													</li>
													<li data-menu-item-id="3e349ad76a29de800ba11951eee69b60"
														class="brz-menu__item"><a class="brz-a" target=""
															href="/mt-blog" title="Blog"><span
																class="brz-span">Blog</span></a></li>
													<li data-menu-item-id="ccf214e2ed986735e372c15ea2c09523"
														class="brz-menu__item"><a class="brz-a" target=""
															href="/contact" title="Επικοινωνία"><span
																class="brz-span">Επικοινωνία</span></a></li>
												</ul>
											</nav>
											<div class="brz-mm-menu__icon"><svg class="brz-icon-svg align-[initial]">
													<use
														href="../assets/svg/8e85539e41ed2686da0dcff2d601af13.svg#brz_icon">
													</use>
												</svg></div>
											<nav data-mods="%7B%22desktop%22%3A%22horizontal%22%2C%22tablet%22%3A%22horizontal%22%2C%22mobile%22%3A%22horizontal%22%7D"
												id="fbiyjwwellhvtbfzvawrysdglhnvhbgmljyw_ie915c2c30027576af07b"
												class="brz-menu brz-menu__preview brz-menu__mmenu brz-menu--has-dropdown brz-css-12dn42o brz-css-1x5bkh9">
												<ul class="brz-menu__ul">
													<li data-menu-item-id="b22b960322341a5e734d1324bbd76ed2"
														class="brz-menu__item"><a class="brz-a" target="" href="/mt"
															title="Μουσικοθεραπεία"><span
																class="brz-span">Μουσικοθεραπεία</span></a>
														<ul class="brz-menu__sub-menu">
															<li data-menu-item-id="01d48bae25f7229912cdc6bd654489a6"
																class="brz-menu__item"><a class="brz-a" target=""
																	href="/find" title="Βρες Μουσικοθεραπευτή"><span
																		class="brz-span">Βρες
																		Μουσικοθεραπευτή</span></a></li>
															<li data-menu-item-id="ead41e4a9b5e03ba07aeb9e426f4c21a"
																class="brz-menu__item"><a class="brz-a" target=""
																	href="/study" title="Σπουδές Μουσικοθεραπείας"><span
																		class="brz-span">Σπουδές
																		Μουσικοθεραπείας</span></a></li>
															<li data-menu-item-id="602acc623566a539fa600e1a24add4a0"
																class="brz-menu__item"><a class="brz-a" target=""
																	href="/mt-info"
																	title="Πληροφορίες για Μουσικοθεραπευτές"><span
																		class="brz-span">Πληροφορίες για
																		Μουσικοθεραπευτές</span></a></li>
														</ul>
													</li>
													<li data-menu-item-id="3e349ad76a29de800ba11951eee69b60"
														class="brz-menu__item"><a class="brz-a" target=""
															href="/mt-blog" title="Blog"><span
																class="brz-span">Blog</span></a></li>
													<li data-menu-item-id="ccf214e2ed986735e372c15ea2c09523"
														class="brz-menu__item"><a class="brz-a" target=""
															href="/contact" title="Επικοινωνία"><span
																class="brz-span">Επικοινωνία</span></a></li>
												</ul>
											</nav>
										</div>
									</div>
									<div id="" class="brz-css-1tjbyob brz-css-19ioiov brz-wrapper">
										<div class="brz-menu__container brz-css-19uzdaw brz-css-1mxc0dk"
											data-mmenu-id="#tkCzeDJsTpP8_ie915c2c30027576af07b"
											data-mmenu-position="position-left" data-mmenu-title=""
											data-mmenu-stickytitle="on"
											data-mmenu-closingicon="%7B%22desktop%22%3A%22off%22%2C%22tablet%22%3A%22off%22%2C%22mobile%22%3A%22off%22%7D"
											data-brz-custom-id="tkCzeDJsTpP8">
											<nav data-mods="%7B%22desktop%22%3A%22horizontal%22%2C%22tablet%22%3A%22horizontal%22%2C%22mobile%22%3A%22horizontal%22%7D"
												class="brz-menu brz-menu__preview brz-css-c39cel brz-css-1dgf3el">
												<ul class="brz-menu__ul">
													<li data-menu-item-id="68498357bd9751cca09ce585b751e087"
														class="brz-menu__item"><a class="brz-a" target="" href="/mt"
															title="Μουσικοθεραπεία"><span
																class="brz-span">Μουσικοθεραπεία</span></a>
														<ul class="brz-menu__sub-menu">
															<li data-menu-item-id="75d4d1dc5b0c65580e43666ccee4f7a7"
																class="brz-menu__item"><a class="brz-a" target=""
																	href="/find" title="Βρες Μουσικοθεραπευτή"><span
																		class="brz-span">Βρες
																		Μουσικοθεραπευτή</span></a></li>
															<li data-menu-item-id="8b426b54feb315f34ab6402a783f37ea"
																class="brz-menu__item"><a class="brz-a" target=""
																	href="/study" title="Σπουδές Μουσικοθεραπείας"><span
																		class="brz-span">Σπουδές
																		Μουσικοθεραπείας</span></a></li>
															<li data-menu-item-id="079c12ac444e6db44d0c1aef67233f3b"
																class="brz-menu__item"><a class="brz-a" target=""
																	href="/mt-info"
																	title="Πληροφορίες για Μουσικοθεραπευτές"><span
																		class="brz-span">Πληροφορίες για
																		Μουσικοθεραπευτές</span></a></li>
														</ul>
													</li>
													<li data-menu-item-id="54aaa9e0763f70d42976f10caca42b9e"
														class="brz-menu__item"><a class="brz-a" target=""
															href="/mt-blog" title="Blog"><span
																class="brz-span">Blog</span></a></li>
													<li data-menu-item-id="8db2f3adb442538b322f3a1e9822fb29"
														class="brz-menu__item"><a class="brz-a" target=""
															href="/contact" title="Επικοινωνία"><span
																class="brz-span">Επικοινωνία</span></a></li>
													<li data-menu-item-id="0c5b64c5539a611a193d2ef63578b507"
														class="brz-menu__item"><a class="brz-a" target=""
															href="/en/home" title="EN"><svg
																class="brz-icon-svg align-[initial]">
																<use
																	href="../assets/svg/83309393473f0d678e4bb3660c2ccfea.svg#nc_icon">
																</use>
															</svg><span class="brz-span">EN</span></a></li>
												</ul>
											</nav>
											<div class="brz-mm-menu__icon"><svg class="brz-icon-svg align-[initial]">
													<use
														href="../assets/svg/8e85539e41ed2686da0dcff2d601af13.svg#brz_icon">
													</use>
												</svg></div>
											<nav data-mods="%7B%22desktop%22%3A%22horizontal%22%2C%22tablet%22%3A%22horizontal%22%2C%22mobile%22%3A%22horizontal%22%7D"
												id="tkCzeDJsTpP8_ie915c2c30027576af07b"
												class="brz-menu brz-menu__preview brz-menu__mmenu brz-menu--has-dropdown brz-css-12dn42o brz-css-1kwae1d">
												<ul class="brz-menu__ul">
													<li data-menu-item-id="68498357bd9751cca09ce585b751e087"
														class="brz-menu__item"><a class="brz-a" target="" href="/mt"
															title="Μουσικοθεραπεία"><span
																class="brz-span">Μουσικοθεραπεία</span></a>
														<ul class="brz-menu__sub-menu">
															<li data-menu-item-id="75d4d1dc5b0c65580e43666ccee4f7a7"
																class="brz-menu__item"><a class="brz-a" target=""
																	href="/find" title="Βρες Μουσικοθεραπευτή"><span
																		class="brz-span">Βρες
																		Μουσικοθεραπευτή</span></a></li>
															<li data-menu-item-id="8b426b54feb315f34ab6402a783f37ea"
																class="brz-menu__item"><a class="brz-a" target=""
																	href="/study" title="Σπουδές Μουσικοθεραπείας"><span
																		class="brz-span">Σπουδές
																		Μουσικοθεραπείας</span></a></li>
															<li data-menu-item-id="079c12ac444e6db44d0c1aef67233f3b"
																class="brz-menu__item"><a class="brz-a" target=""
																	href="/mt-info"
																	title="Πληροφορίες για Μουσικοθεραπευτές"><span
																		class="brz-span">Πληροφορίες για
																		Μουσικοθεραπευτές</span></a></li>
														</ul>
													</li>
													<li data-menu-item-id="54aaa9e0763f70d42976f10caca42b9e"
														class="brz-menu__item"><a class="brz-a" target=""
															href="/mt-blog" title="Blog"><span
																class="brz-span">Blog</span></a></li>
													<li data-menu-item-id="8db2f3adb442538b322f3a1e9822fb29"
														class="brz-menu__item"><a class="brz-a" target=""
															href="/contact" title="Επικοινωνία"><span
																class="brz-span">Επικοινωνία</span></a></li>
													<li data-menu-item-id="0c5b64c5539a611a193d2ef63578b507"
														class="brz-menu__item"><a class="brz-a" target=""
															href="/en/home" title="EN"><svg
																class="brz-icon-svg align-[initial] brz-mm-menu__item__icon">
																<use
																	href="../assets/svg/83309393473f0d678e4bb3660c2ccfea.svg#nc_icon">
																</use>
															</svg><span class="brz-span">EN</span></a></li>
												</ul>
											</nav>
										</div>
									</div>
								</div>
							</div>
							<div id="translatorclick" class="brz-columns brz-css-1csnpdv brz-css-jow5dd"
								data-brz-custom-id="ofxnnpp8wVND">
								<div class="brz-column__items brz-css-1pzte4r brz-css-190lc1z">
									<div id="" class="brz-css-1tjbyob brz-css-xmdiqp brz-wrapper">
										<div class="brz-wrapper-transform">
											<div class="brz-icon-text brz-css-nntapz brz-css-1umndxb"
												data-brz-custom-id="gsFk4ZWbjT9L">
												<div class="brz-icon__container" data-brz-custom-id="oNVhNf89QSOh"><span
														class="brz-icon brz-span brz-css-e9bk1k brz-css-h3q0h5"><svg
															class="brz-icon-svg align-[initial]">
															<use
																href="../assets/svg/673cf948d20838cde5e7c8f64474298f.svg#nc_icon">
															</use>
														</svg></span></div>
												<div class="brz-text-btn">
													<div class="brz-rich-text brz-rich-text__custom brz-css-10fnxcx brz-css-1n6htfm"
														data-brz-custom-id="k7MTYkg143MN">
														<div data-brz-translate-text="1">
															<p class="brz-mt-lg-0 brz-text-lg-justify brz-vfw-lg-400 brz-fwdth-lg-100 brz-fsft-lg-0 brz-tp-lg-empty brz-ff-comfortaa brz-ft-google brz-fs-lg-16 brz-fss-lg-px brz-fw-lg-400 brz-ls-lg-m_0_2 brz-lh-lg-1_7 brz-css-aQaKb"
																data-uniq-id="yAzBt" data-generated-css="brz-css-wO7DX">
																<span style="color: rgba(var(--brz-global-color7),1);"
																	class="brz-cp-color7">EN</span></p>
														</div>
													</div>
												</div>
											</div>
										</div>
									</div>
									<div id="" class="brz-css-1tjbyob brz-css-r8i1ey brz-wrapper">
										<div class="brz-embed-code brz-css-zwba1n brz-css-3bcg12"
											data-brz-custom-id="dkIB9yIrtPL_">
											<div class="brz-embed-content">
												<div>
													<style>
														#translatorclick {
															transition: transform .3s ease-in-out;
														}

														#translatorclick:hover {
															transform: scale(1.08);
														}
													</style>
												</div>
											</div>
										</div>
									</div>
								</div><a class="brz-a brz-container-link" href="/en/home" target="_self" rel="noopener"
									data-brz-link-type="external"></a>
							</div>
						</div>
					</header>
				</div>
			</div>
		</section>
	</div>
</body>

</html>